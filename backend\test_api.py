#!/usr/bin/env python3
import requests
import json

# Test the registration endpoint
url = "http://127.0.0.1:8000/api/accounts/register/"

# Test data that should work
test_data = {
    "username": "teststudent2025",
    "password": "password123",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "email": "<EMAIL>",
    "code": "8a151a4c-a821-4541-af7a-3940cbf9326b"
}

try:
    # Test with CORS headers like the frontend would send
    headers = {
        'Origin': 'http://localhost:5174',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    response = requests.post(url, json=test_data, headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    print(f"Response Content: {response.text}")

    if response.status_code in [200, 201]:
        data = response.json()
        print(f"SUCCESS! JSON Data: {json.dumps(data, indent=2)}")
    else:
        print(f"FAILED! Status: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error Details: {json.dumps(error_data, indent=2)}")
        except:
            print(f"Raw Error: {response.text}")

except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")
