// frontend/src/utils/manuscriptTemplate.js

// This is a pre-defined Tiptap document structure for a new manuscript.
export const manuscriptTemplate = {
  type: "doc",
  content: [
    {
      type: "heading",
      attrs: { level: 1 },
      content: [{ type: "text", text: "New Manuscript Title" }],
    },
    {
      type: "paragraph",
    },
    {
      type: "heading",
      attrs: { level: 2 },
      content: [{ type: "text", text: "Abstract" }],
    },
    {
      type: "paragraph",
    },
    {
      type: "heading",
      attrs: { level: 2 },
      content: [{ type: "text", text: "Introduction" }],
    },
    {
      type: "paragraph",
    },
    // You can add all the other sections (Methods, Results, etc.) here
  ],
};



