# backend/accounts/serializers.py

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.core.validators import validate_email as django_validate_email
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from .models import (
    Institution, Department, ResearchGroup, RoleType,
    CustomUser, UserRole, ResearchGroupMembership, Invitation
)
from django.db import transaction
# ==============================================================================
# SECTION 1: HELPER & NESTED SERIALIZERS
# ==============================================================================

class SimpleUserSerializer(serializers.ModelSerializer):
    """A simple, read-only serializer for displaying basic user info."""
    primary_role_name = serializers.CharField(source='primary_role.name', read_only=True)
    class Meta:
        model = CustomUser
        fields = ['id', 'username', 'first_name', 'last_name', 'email', 'primary_role_name']

class RoleTypeSerializer(serializers.ModelSerializer):
    """Serializer for the RoleType model."""
    class Meta:
        model = RoleType
        fields = ['id', 'name', 'can_supervise', 'can_invite']

class DepartmentSerializer(serializers.ModelSerializer):
    """Serializer for the Department model."""
    class Meta:
        model = Department
        fields = ['id', 'name', 'institution']

class ResearchGroupSerializer(serializers.ModelSerializer):
    """Serializer for the ResearchGroup model."""
    class Meta:
        model = ResearchGroup
        fields = ['id', 'name', 'department']

class InstitutionSerializer(serializers.ModelSerializer):
    """Serializer for the Institution model."""
    class Meta:
        model = Institution
        fields = ['id', 'name', 'created_at']

# ==============================================================================
# SECTION 2: USER & MEMBERSHIP SERIALIZERS
# ==============================================================================

class UserRoleSerializer(serializers.ModelSerializer):
    role_type = RoleTypeSerializer(read_only=True)
    class Meta:
        model = UserRole
        fields = ['id', 'role_type', 'department', 'is_primary', 'is_active']

class UserDetailSerializer(serializers.ModelSerializer):
    roles = UserRoleSerializer(many=True, read_only=True)
    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'first_name', 'last_name', 'email',
            'primary_role', 'primary_department', 'roles'
        ]

# ==============================================================================
# SECTION 3: AUTHENTICATION & REGISTRATION SERIALIZERS
# ==============================================================================

class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['username'] = user.username
        if user.primary_role:
            token['primary_role'] = user.primary_role.name
            token['can_supervise'] = user.primary_role.can_supervise
            token['can_invite'] = user.primary_role.can_invite
        return token

class StudentRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True)
    invitation_code = serializers.UUIDField(write_only=True, required=True)
    class Meta:
        model = CustomUser
        fields = ('username', 'password', 'email', 'first_name', 'last_name', 'invitation_code')
    
    # ... (You will add the validate and create methods here later) ...

class InvitationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Invitation
        fields = '__all__'
        
        
        

# # ==============================================================================
# # --- ADD THIS NEW SERIALIZER FOR INSTITUTIONAL ONBOARDING ---
# # ==============================================================================

class InstitutionRegistrationSerializer(serializers.Serializer):
    """
    Handles the creation of a new Institution, its first Department,
    and its first Administrator user.
    This is a non-model serializer because it creates multiple objects.
    """
    # Fields the frontend form will send
    institution_name = serializers.CharField(max_length=255)
    admin_first_name = serializers.CharField(max_length=150)
    admin_last_name = serializers.CharField(max_length=150)
    admin_email = serializers.EmailField()
    admin_username = serializers.CharField(max_length=150)
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})

    def validate_institution_name(self, value):
        """Check that the institution name is not already taken."""
        if Institution.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError("An institution with this name already exists.")
        return value

    def validate_admin_username(self, value):
        """Check that the admin username is not already taken."""
        if CustomUser.objects.filter(username__iexact=value).exists():
            raise serializers.ValidationError("This username is already taken.")
        return value
        
    def validate_admin_email(self, value):
        """Check that the admin email is not already taken."""
        if CustomUser.objects.filter(email__iexact=value).exists():
            raise serializers.ValidationError("An account with this email already exists.")
        return value

    @transaction.atomic # This ensures all database operations succeed or none do
    def create(self, validated_data):
        """
        Creates the Institution, admin RoleType, first Department,
        the admin CustomUser, their UserRole, and their GroupMembership.
        """
        # 1. Create the Institution
        institution = Institution.objects.create(name=validated_data['institution_name'])

        # 2. Create the "Institution Administrator" RoleType with full permissions
        admin_role, _ = RoleType.objects.get_or_create(
            name='Institution Administrator',
            defaults={'can_supervise': True, 'can_invite': True}
        )
        
        # 3. Create a default first Department for the Institution
        department = Department.objects.create(
            name="Main Office",
            institution=institution
        )

        # 4. Create the administrator's user account
        admin_user = CustomUser.objects.create_user(
            username=validated_data['admin_username'],
            password=validated_data['password'],
            email=validated_data['admin_email'],
            first_name=validated_data['admin_first_name'],
            last_name=validated_data['admin_last_name'],
            primary_role=admin_role,
            primary_department=department
        )
        
        # 5. Assign the UserRole to the new admin
        UserRole.objects.create(
            user=admin_user,
            role_type=admin_role,
            department=department,
            is_primary=True,
            is_active=True
        )

        # Note: We don't create a ResearchGroup or Membership here, as the
        # Institution Admin's job is to create those later.

        return admin_user # Return the created user






# # AcademiaSpace/backend/accounts/serializers.py

# from rest_framework import serializers
# from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
# from django.utils import timezone
# from .models import (
#     Institution, Department, ResearchGroup, RoleType,
#     CustomUser, UserRole, ResearchGroupMembership, Invitation
# )

# from django.db import transaction

# # ==============================================================================
# # SECTION 1: HELPER & NESTED SERIALIZERS
# # ==============================================================================

# class SimpleUserSerializer(serializers.ModelSerializer):
#     """A simple, read-only serializer for displaying basic user info."""
#     class Meta:
#         model = CustomUser
#         fields = ['id', 'username', 'first_name', 'last_name', 'email', 'primary_role']


# class RoleTypeSerializer(serializers.ModelSerializer):
#     """Serializer for the RoleType model."""
#     class Meta:
#         model = RoleType
#         fields = ['id', 'name', 'can_supervise', 'can_invite']


# class DepartmentSerializer(serializers.ModelSerializer):
#     """Serializer for the Department model."""
#     class Meta:
#         model = Department
#         fields = ['id', 'name']

class SimpleResearchGroupSerializer(serializers.ModelSerializer):
    """A simple serializer for displaying a research group's basic info."""
    class Meta:
        model = ResearchGroup
        fields = ['id', 'name']

# # class SimpleResearchGroupSerializer(serializers.ModelSerializer):
# #     """A simple serializer for displaying research group info."""
# #     class Meta:
# #         model = ResearchGroup
# #         fields = ['id', 'name']


# # ==============================================================================
# # SECTION 2: USER & MEMBERSHIP SERIALIZERS
# # ==============================================================================

# class UserRoleSerializer(serializers.ModelSerializer):
#     """Serializer for displaying a user's role within a department."""
#     role_type = RoleTypeSerializer(read_only=True)
#     department = DepartmentSerializer(read_only=True)

#     class Meta:
#         model = UserRole
#         fields = ['id', 'role_type', 'department', 'is_primary', 'is_active']


class ResearchGroupMembershipSerializer(serializers.ModelSerializer):
    """Serializer for displaying a user's membership in a research group."""
    class Meta:
        model = ResearchGroupMembership
        fields = ['id', 'research_group', 'status', 'date_joined']


# class UserDetailSerializer(serializers.ModelSerializer):
#     """A comprehensive, read-only serializer for a user's profile."""
#     roles = UserRoleSerializer(many=True, read_only=True)
#     group_memberships = ResearchGroupMembershipSerializer(many=True, read_only=True)

#     class Meta:
#         model = CustomUser
#         fields = [
#             'id', 'username', 'first_name', 'last_name', 'email',
#             'primary_role', 'primary_department', 'profile_picture',
#             'roles', 'group_memberships'
#         ]


# # ==============================================================================
# # SECTION 3: AUTHENTICATION & REGISTRATION SERIALIZERS
# # ==============================================================================

# class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
#     """
#     Customizes the JWT token to include the user's role and other essential info,
#     which empowers the frontend to manage UI visibility and permissions.
#     """
#     @classmethod
#     def get_token(cls, user):
#         token = super().get_token(user)
#         # Add custom claims to the token's payload
#         token['username'] = user.username
#         if user.primary_role:
#             token['primary_role'] = user.primary_role.name
#             token['can_supervise'] = user.primary_role.can_supervise
#             token['can_invite'] = user.primary_role.can_invite
#         return token



# class StudentRegistrationSerializer(serializers.ModelSerializer):
#     """
#     Serializer for a new STUDENT registering using an invitation code.
#     This is the one that was missing.
#     """
#     password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
#     invitation_code = serializers.UUIDField(write_only=True, required=True)

#     class Meta:
#         model = CustomUser
#         fields = ('username', 'password', 'email', 'first_name', 'last_name', 'invitation_code')

#     def validate(self, data):
#         """Check that the invitation code is valid and the email matches."""
#         try:
#             invitation = Invitation.objects.get(code=data['invitation_code'], is_accepted=False)
#             if invitation.invitee_email.lower() != data['email'].lower():
#                 raise serializers.ValidationError({"email": "This email does not match the invitation."})
#             data['invitation'] = invitation # Pass the validated invitation object to the create method
#             return data
#         except Invitation.DoesNotExist:
#             raise serializers.ValidationError({"invitation_code": "This invitation code is invalid or has already been used."})

#     @transaction.atomic
#     def create(self, validated_data):
#         """Create the user and all their related role and group assignments."""
#         invitation = validated_data.pop('invitation')
#         inviter = invitation.inviter
        
#         # A student's group and department are inherited from their inviter
#         membership = inviter.group_memberships.filter(status='ACTIVE').first()
#         if not membership:
#             raise serializers.ValidationError("The inviter is not a member of any active research group.")
#         research_group = membership.research_group
        
#         student_role_type, _ = RoleType.objects.get_or_create(name='Student')
        
#         user = CustomUser.objects.create_user(
#             username=validated_data['username'],
#             password=validated_data['password'],
#             email=invitation.invitee_email,
#             first_name=validated_data.get('first_name', ''),
#             last_name=validated_data.get('last_name', ''),
#             primary_role=student_role_type,
#             primary_department=research_group.department
#         )

#         invitation.is_accepted = True
#         invitation.invitee = user
#         invitation.save()

#         UserRole.objects.create(user=user, role_type=student_role_type, department=research_group.department, is_primary=True)
#         ResearchGroupMembership.objects.create(person=user, research_group=research_group, status='ACTIVE', invited_by=inviter)

#         return user


# # ==============================================================================
# # SECTION 4: INVITATION MANAGEMENT SERIALIZERS
# # ==============================================================================


# from rest_framework import serializers
# from django.core.validators import validate_email
# from django.core.exceptions import ValidationError
# from accounts.models import Invitation, CustomUser, ResearchGroup
# from django.utils.translation import gettext_lazy as _

class InvitationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for a researcher to create a new invitation.
    Handles validation to prevent duplicate invitations and invalid emails.
    """
    email = serializers.EmailField(
        source='invitee_email',
        max_length=255,
        help_text="Email address of the person to invite"
    )
    
    # Optional fields with defaults
    intended_role = serializers.PrimaryKeyRelatedField(
        queryset=RoleType.objects.all(),
        required=True,
        help_text="Role to assign to the invited user"
    )
    
    research_group = serializers.PrimaryKeyRelatedField(
        queryset=ResearchGroup.objects.all(),
        required=False,
        allow_null=True,
        help_text="Research group to add the user to (optional)"
    )
    


    class Meta:
        model = Invitation
        fields = ['email', 'intended_role', 'research_group']
        read_only_fields = ['invited_by', 'created_at', 'status']
    
    def validate_email(self, value):
        """
        Validate that the email is not already registered and not already invited.
        """
        # Check if email is already associated with a user
        if CustomUser.objects.filter(email=value).exists():
            raise serializers.ValidationError(
                _("A user with this email address already exists.")
            )
        
        # Check for pending invitations to the same email
        if Invitation.objects.filter(
            invitee_email=value, 
            status=Invitation.Status.PENDING
        ).exists():
            raise serializers.ValidationError(
                _("A pending invitation already exists for this email address.")
            )
        
        # Basic email validation
        try:
            django_validate_email(value)
        except ValidationError:
            raise serializers.ValidationError(_("Please enter a valid email address."))
        
        return value
    
    def validate_research_group(self, value):
        """
        Validate that the current user has permission to add members to this research group.
        """
        request = self.context.get('request')
        if value and request and request.user:
            # Check if user is a member of the research group
            from .models import ResearchGroupMembership
            if not value.memberships.filter(person=request.user, status=ResearchGroupMembership.Status.ACTIVE).exists():
                raise serializers.ValidationError(
                    _("You must be a member of the research group to invite users to it.")
                )
        return value
    
    def create(self, validated_data):
        """
        Create a new invitation, automatically setting the invited_by field
        from the request context.
        """
        request = self.context.get('request')
        if request and request.user:
            validated_data['invited_by'] = request.user
        
        # Set default status
        validated_data['status'] = Invitation.Status.PENDING
        
        return super().create(validated_data)
    
    def to_representation(self, instance):
        """
        Custom representation to include more details when serializing output.
        """
        representation = super().to_representation(instance)
        
        # Add additional fields for the response
        representation['id'] = instance.id
        representation['status'] = instance.status
        representation['status_display'] = instance.get_status_display()
        representation['created_at'] = instance.created_at
        representation['expires_at'] = instance.expires_at
        
        if instance.invited_by:
            representation['invited_by'] = {
                'id': instance.invited_by.id,
                'name': instance.invited_by.get_full_name() or instance.invited_by.username
            }
        
        if instance.research_group:
            representation['research_group'] = {
                'id': instance.research_group.id,
                'name': instance.research_group.name
            }
        
        return representation


# --- REPLACE your old StudentRegistrationSerializer with this one ---
class InvitedUserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for a new user registering with an invitation code.
    This handles the final step of the onboarding workflow.
    """
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    # The user provides the unique code from their email.
    code = serializers.UUIDField(write_only=True, required=True)

    class Meta:
        model = CustomUser
        fields = ('username', 'password', 'first_name', 'last_name', 'email', 'code')

    def validate(self, data):
        """
        Check that the invitation code is valid, not expired, and the email matches.
        """
        try:
            invitation = Invitation.objects.get(code=data['code'], status=Invitation.Status.PENDING)
            
            # Check if expired
            if not invitation.is_valid():
                raise serializers.ValidationError({"code": "This invitation has expired."})
                
            # Check if email matches
            if invitation.invitee_email.lower() != data['email'].lower():
                raise serializers.ValidationError({"email": "This email does not match the one on the invitation."})
            
            # Attach the valid invitation object for use in the create method
            data['invitation'] = invitation
            return data
        except Invitation.DoesNotExist:
            raise serializers.ValidationError({"code": "This invitation code is invalid or has already been used."})

    def create(self, validated_data):
        """
        Create the user, link them to the group, create their roles,
        and mark the invitation as accepted.
        """
        invitation = validated_data.pop('invitation')
        inviter = invitation.invited_by
        research_group = invitation.research_group
        intended_role = invitation.intended_role
        
        # Create the new user
        user = CustomUser.objects.create_user(
            username=validated_data['username'],
            password=validated_data['password'],
            email=invitation.invitee_email,
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', ''),
            primary_role=intended_role,
            primary_department=research_group.department
        )

        # Mark the invitation as accepted
        invitation.status = Invitation.Status.ACCEPTED
        invitation.invitee = user
        invitation.accepted_at = timezone.now()
        invitation.save()

        # Create the primary UserRole for the new student/researcher
        UserRole.objects.create(
            user=user,
            role_type=intended_role,
            department=research_group.department,
            is_primary=True,
            is_active=True
        )

        # Create the ResearchGroupMembership for the new user
        ResearchGroupMembership.objects.create(
            person=user,
            research_group=research_group,
            status=ResearchGroupMembership.Status.ACTIVE,
            invited_by=inviter
        )

        return user



# class InvitationSerializer(serializers.ModelSerializer):
#     """
#     A comprehensive serializer for creating and viewing invitations.
#     """
#     inviter = SimpleUserSerializer(read_only=True)
#     invitee = SimpleUserSerializer(read_only=True)
#     research_group = SimpleResearchGroupSerializer(read_only=True)
#     intended_role = RoleTypeSerializer(read_only=True)

#     # For writing, the frontend only needs to provide the email and role ID
#     email = serializers.EmailField(source='invitee_email', write_only=True)
#     role_type_id = serializers.PrimaryKeyRelatedField(
#         queryset=RoleType.objects.all(), source='intended_role', write_only=True
#     )

#     class Meta:
#         model = Invitation
#         fields = [
#             'id', 'code', 'invitee_email', 'email', 'intended_role', 'role_type_id',
#             'status', 'invited_by', 'research_group', 'invitee',
#             'created_at', 'expires_at', 'accepted_at'
#         ]
#         read_only_fields = [
#             'id', 'code', 'status', 'invited_by', 'research_group', 'invitee',
#             'created_at', 'expires_at', 'accepted_at'
#         ]



class InvitationAcceptSerializer(serializers.Serializer):
    """
    Serializer for a new user to accept an invitation and complete registration.
    """
    code = serializers.UUIDField(write_only=True)
    username = serializers.CharField(max_length=150, required=True)
    first_name = serializers.CharField(max_length=150, required=True)
    last_name = serializers.CharField(max_length=150, required=True)
    password = serializers.CharField(min_length=8, write_only=True)

    def validate_code(self, value):
        """Validate that the code is valid and pending."""
        try:
            invitation = Invitation.objects.get(code=value, status=Invitation.Status.PENDING)
            if timezone.now() > invitation.expires_at:
                raise serializers.ValidationError("This invitation has expired.")
            return invitation
        except Invitation.DoesNotExist:
            raise serializers.ValidationError("Invalid or already accepted invitation code.")

    def create(self, validated_data):
        """
        Create the new user account, link everything, and mark invitation as accepted.
        """
        invitation = validated_data.pop('code')
        
        # Create the user
        user = CustomUser.objects.create_user(
            username=validated_data['username'],
            email=invitation.invitee_email, # Use the invited email for security
            password=validated_data['password'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            primary_role=invitation.intended_role,
            primary_department=invitation.research_group.department
        )
        
        # Create the primary UserRole
        UserRole.objects.create(
            user=user,
            role_type=invitation.intended_role,
            department=invitation.research_group.department,
            is_primary=True
        )
        
        # Create the ResearchGroupMembership
        ResearchGroupMembership.objects.create(
            person=user,
            research_group=invitation.research_group,
            invited_by=invitation.invited_by,
            status=ResearchGroupMembership.Status.ACTIVE
        )
        
        # Update invitation status
        invitation.status = Invitation.Status.ACCEPTED
        invitation.accepted_at = timezone.now()
        invitation.invitee = user
        invitation.save()
        
        return user

