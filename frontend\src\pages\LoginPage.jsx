// frontend/src/pages/LoginPage.jsx

import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { Eye, EyeOff, LogIn, Loader2 } from "lucide-react";
import { jwtDecode } from "jwt-decode";

const LoginPage = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect if already authenticated (disabled to prevent conflicts with login redirect)
  useEffect(() => {
    if (isAuthenticated && user) {
      // Only redirect if we're on the login page and user is already authenticated
      if (location.pathname === '/login' || location.pathname === '/') {
        // Let AuthRedirect handle the role-based routing
        const from = location.state?.from?.pathname || "/";
        console.log("🔍 DEBUG: useEffect redirect to:", from);
        navigate(from, { replace: true });
      }
    }
  }, [isAuthenticated, user, navigate, location]);

  // We'll determine the redirect path after login based on user role

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      await login(formData.username, formData.password);

      // After successful login, get the updated user data from localStorage token
      const token = localStorage.getItem("access_token");
      console.log("🔍 DEBUG: Token retrieved:", token ? "Token exists" : "No token");

      if (token) {
        try {
          const decodedUser = jwtDecode(token);
          console.log("🔍 DEBUG: Full decoded user object:", decodedUser);
          console.log("🔍 DEBUG: can_supervise:", decodedUser?.can_supervise);
          console.log("🔍 DEBUG: can_invite:", decodedUser?.can_invite);
          console.log("🔍 DEBUG: primary_role:", decodedUser?.primary_role);

          // Let AuthRedirect handle the role-based routing
          const from = location.state?.from?.pathname || "/";
          console.log("🔍 DEBUG: Redirecting to:", from);

          navigate(from, { replace: true });
        } catch (decodeError) {
          console.error("Failed to decode token for redirect:", decodeError);
          // ALWAYS redirect to the root. The AuthRedirect component will handle the rest.
          navigate('/', { replace: true });
        }
      } else {
        // ALWAYS redirect to the root. The AuthRedirect component will handle the rest.
        navigate('/', { replace: true });
      }
    } catch (err) {
      console.error("Login error:", err);
      setError(err.response?.data?.message || "Login failed. Please check your credentials and try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Demo credentials hint (remove in production)
  const isDevelopment = process.env.NODE_ENV === "development";

  useEffect(() => {
    // This code runs only after the component has mounted
    // and the browser has had time to process the CSS.
    const existingStyle = document.getElementById("spinner-animation");
    if (!existingStyle) {
      const styleElement = document.createElement("style");
      styleElement.id = "spinner-animation";
      styleElement.innerHTML = `
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
      document.head.appendChild(styleElement);
    }
  }, []); 

  
  return (
    <div style={styles.container}>
      <div style={styles.formContainer}>
        <div style={styles.header}>
          <h1 style={styles.title}>AcademiaSpace</h1>
          <p style={styles.subtitle}>Sign in to your account</p>
        </div>

        <form onSubmit={handleSubmit} style={styles.form}>
          <div style={styles.inputGroup}>
            <label htmlFor="username" style={styles.label}>
              Username
            </label>
            <input
              id="username"
              name="username"
              type="text"
              value={formData.username}
              onChange={handleChange}
              placeholder="Enter your username"
              required
              disabled={isLoading}
              style={styles.input}
              autoComplete="username"
            />
          </div>

          <div style={styles.inputGroup}>
            <label htmlFor="password" style={styles.label}>
              Password
            </label>
            <div style={styles.passwordContainer}>
              <input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={handleChange}
                placeholder="Enter your password"
                required
                disabled={isLoading}
                style={{ ...styles.input, paddingRight: "45px" }}
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                style={styles.passwordToggle}
                disabled={isLoading}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          {error && (
            <div style={styles.errorContainer}>
              <p style={styles.error}>{error}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            style={{
              ...styles.button,
              ...(isLoading && styles.buttonLoading),
            }}
          >
            {isLoading ? (
              <>
                <Loader2 size={20} style={styles.spinner} />
                Signing in...
              </>
            ) : (
              <>
                <LogIn size={20} />
                Sign In
              </>
            )}
          </button>

          {isDevelopment && (
            <div style={styles.demoHint}>
              <p style={styles.demoText}>
                <strong>Demo:</strong> Try admin/admin or user/user
              </p>
            </div>
          )}

          <div style={styles.footer}>
            <p style={styles.footerText}>
              Don't have an account?{" "}
              <Link to="/register" style={styles.link}>
                Sign up here
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

const styles = {
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    minHeight: "100vh",
    backgroundColor: "#f8fafc",
    padding: "20px",
  },
  formContainer: {
    padding: "2.5rem",
    background: "white",
    borderRadius: "12px",
    boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
    width: "100%",
    maxWidth: "400px",
    border: "1px solid #e2e8f0",
  },
  header: {
    textAlign: "center",
    marginBottom: "2rem",
  },
  title: {
    fontSize: "1.875rem",
    fontWeight: "700",
    color: "#1e293b",
    margin: "0 0 0.5rem 0",
  },
  subtitle: {
    fontSize: "1rem",
    color: "#64748b",
    margin: "0",
  },
  form: {
    display: "flex",
    flexDirection: "column",
    gap: "1.25rem",
  },
  inputGroup: {
    display: "flex",
    flexDirection: "column",
    gap: "0.5rem",
  },
  label: {
    fontSize: "0.875rem",
    fontWeight: "500",
    color: "#374151",
  },
  input: {
    padding: "0.75rem 1rem",
    borderRadius: "8px",
    border: "1px solid #d1d5db",
    fontSize: "1rem",
    transition: "all 0.2s ease",
    outline: "none",
    width: "100%",
    boxSizing: "border-box",
    ":focus": {
      borderColor: "#3b82f6",
      boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
    },
    ":disabled": {
      backgroundColor: "#f9fafb",
      cursor: "not-allowed",
    },
  },
  passwordContainer: {
    position: "relative",
    display: "flex",
    alignItems: "center",
  },
  passwordToggle: {
    position: "absolute",
    right: "12px",
    background: "none",
    border: "none",
    color: "#64748b",
    cursor: "pointer",
    padding: "4px",
    borderRadius: "4px",
    ":hover": {
      color: "#374151",
      backgroundColor: "#f1f5f9",
    },
    ":disabled": {
      cursor: "not-allowed",
      opacity: "0.5",
    },
  },
  errorContainer: {
    backgroundColor: "#fef2f2",
    border: "1px solid #fecaca",
    borderRadius: "6px",
    padding: "0.75rem",
  },
  error: {
    color: "#dc2626",
    fontSize: "0.875rem",
    margin: "0",
    textAlign: "center",
  },
  button: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "0.5rem",
    padding: "0.75rem 1.5rem",
    background: "#2563eb",
    color: "white",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
    fontSize: "1rem",
    fontWeight: "600",
    transition: "all 0.2s ease",
    ":hover": {
      background: "#1d4ed8",
    },
    ":disabled": {
      background: "#93c5fd",
      cursor: "not-allowed",
    },
  },
  buttonLoading: {
    opacity: "0.8",
  },
  spinner: {
    animation: "spin 1s linear infinite",
  },
  demoHint: {
    textAlign: "center",
    padding: "0.75rem",
    backgroundColor: "#f0f9ff",
    borderRadius: "6px",
    border: "1px solid #bae6fd",
  },
  demoText: {
    margin: "0",
    fontSize: "0.875rem",
    color: "#0369a1",
  },
  footer: {
    textAlign: "center",
    paddingTop: "1rem",
    borderTop: "1px solid #e2e8f0",
  },
  footerText: {
    margin: "0",
    fontSize: "0.875rem",
    color: "#64748b",
  },
  link: {
    color: "#2563eb",
    textDecoration: "none",
    fontWeight: "500",
    ":hover": {
      textDecoration: "underline",
    },
  },
};

export default LoginPage;




// // frontend/src/pages/LoginPage.jsx

// import React, { useState } from "react";
// import { useNavigate, useLocation } from "react-router-dom";
// import { useAuth } from "../context/AuthContext";

// const LoginPage = () => {
//   const [username, setUsername] = useState("");
//   const [password, setPassword] = useState("");
//   const [error, setError] = useState("");
//   const { login } = useAuth(); // Get the login function from our context
//   const navigate = useNavigate();
//   const location = useLocation();

//   // This gets the page the user was trying to go to before being redirected here
//   const from = location.state?.from?.pathname || "/dashboard";

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setError("");
//     try {
//       // Call the login function from the AuthContext
//       await login(username, password);
//       // On success, navigate to the intended page or the dashboard
//       navigate(from, { replace: true });
//     } catch (err) {
//       console.error(err); // Log the full error for debugging
//       setError("Login failed. Please check your credentials.");
//     }
//   };

//   return (
//     <div style={styles.container}>
//       <div style={styles.formContainer}>
//         <h1 style={{ textAlign: "center" }}>AcademiaSpace</h1>
//         <h2 style={{ textAlign: "center", fontWeight: "normal", marginTop: 0 }}>Login</h2>
//         <form onSubmit={handleSubmit} style={styles.form}>
//           <input
//             id="username"
//             type="text"
//             value={username}
//             onChange={(e) => setUsername(e.target.value)}
//             placeholder="Username"
//             required
//             style={styles.input}
//           />
//           <input
//             id="password"
//             type="password"
//             value={password}
//             onChange={(e) => setPassword(e.target.value)}
//             placeholder="Password"
//             required
//             style={styles.input}
//           />
//           {error && <p style={styles.error}>{error}</p>}
//           <button type="submit" style={styles.button}>
//             Login
//           </button>
//           {/* We will add a link to the registration page here later */}
//         </form>
//       </div>
//     </div>
//   );
// };

// // Simple inline styles for the login page
// const styles = {
//   container: {
//     display: "flex",
//     justifyContent: "center",
//     alignItems: "center",
//     height: "100vh",
//     backgroundColor: "#f0f2f5",
//   },
//   formContainer: {
//     padding: "40px",
//     background: "white",
//     borderRadius: "8px",
//     boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
//     width: "100%",
//     maxWidth: "400px",
//   },
//   form: {
//     display: "flex",
//     flexDirection: "column",
//     gap: "15px",
//   },
//   input: {
//     padding: "12px",
//     borderRadius: "5px",
//     border: "1px solid #ccc",
//     fontSize: "1rem",
//   },
//   button: {
//     padding: "12px",
//     background: "#007bff",
//     color: "white",
//     border: "none",
//     borderRadius: "5px",
//     cursor: "pointer",
//     fontSize: "1rem",
//     fontWeight: "bold",
//   },
//   error: {
//     color: "red",
//     textAlign: "center",
//     margin: 0,
//   },
// };

// export default LoginPage;
