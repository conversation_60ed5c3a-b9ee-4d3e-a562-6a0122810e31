// frontend/src/pages/PapersListPage.jsx
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  getPapers, 
  createPaper, 
  deletePaper 
} from '../services/api';
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Calendar,
  User,
  BarChart3,
  Edit3,
  Trash2,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';

const PapersListPage = () => {
  const [papers, setPapers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    fetchPapers();
  }, []);

  const fetchPapers = async () => {
    try {
      setLoading(true);
      const response = await getPapers();
      setPapers(response.data);
    } catch (err) {
      console.error('Failed to fetch papers:', err);
      setError('Failed to load papers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredPapers = papers.filter(paper => {
    const matchesSearch = paper.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         paper.source_journal?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || paper.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const handleCreatePaper = async (paperData) => {
    try {
      const response = await createPaper(paperData);
      setPapers(prev => [response.data, ...prev]);
      setShowCreateModal(false);
    } catch (err) {
      console.error('Failed to create paper:', err);
      setError('Failed to create paper. Please try again.');
    }
  };

  const handleDeletePaper = async (paperId) => {
    if (!window.confirm('Are you sure you want to delete this paper?')) return;
    
    try {
      await deletePaper(paperId);
      setPapers(prev => prev.filter(paper => paper.id !== paperId));
    } catch (err) {
      console.error('Failed to delete paper:', err);
      setError('Failed to delete paper. Please try again.');
    }
  };

  if (loading) {
    return (
      <div style={styles.loadingContainer}>
        <Loader2 size={32} style={styles.spinner} />
        <p>Loading papers...</p>
      </div>
    );
  }

  return (
    <div style={styles.container}>
      {/* Header */}
      <header style={styles.header}>
        <div style={styles.headerContent}>
          <div>
            <h1 style={styles.title}>Research Papers</h1>
            <p style={styles.subtitle}>
              Manage your research publications and papers
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            style={styles.createButton}
          >
            <Plus size={20} />
            New Paper
          </button>
        </div>
      </header>

      {/* Filters and Search */}
      <div style={styles.controls}>
        <div style={styles.searchContainer}>
          <Search size={20} style={styles.searchIcon} />
          <input
            type="text"
            placeholder="Search papers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={styles.searchInput}
          />
        </div>
        <div style={styles.filterContainer}>
          <Filter size={16} />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            style={styles.filterSelect}
          >
            <option value="all">All Status</option>
            <option value="draft">Draft</option>
            <option value="submitted">Submitted</option>
            <option value="published">Published</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>

      {error && (
        <div style={styles.errorBanner}>
          <AlertCircle size={18} />
          <span>{error}</span>
          <button onClick={() => setError('')} style={styles.dismissError}>
            ×
          </button>
        </div>
      )}

      {/* Papers Grid */}
      <div style={styles.papersGrid}>
        {filteredPapers.length === 0 ? (
          <div style={styles.emptyState}>
            <FileText size={48} />
            <h3>No papers found</h3>
            <p>
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first research paper'
              }
            </p>
            {!searchTerm && filterStatus === 'all' && (
              <button
                onClick={() => setShowCreateModal(true)}
                style={styles.createButton}
              >
                <Plus size={16} />
                Create Paper
              </button>
            )}
          </div>
        ) : (
          filteredPapers.map((paper) => (
            <PaperCard
              key={paper.id}
              paper={paper}
              onDelete={handleDeletePaper}
            />
          ))
        )}
      </div>

      {/* Create Paper Modal */}
      {showCreateModal && (
        <CreatePaperModal
          onClose={() => setShowCreateModal(false)}
          onCreate={handleCreatePaper}
        />
      )}
    </div>
  );
};

const PaperCard = ({ paper, onDelete }) => {
  const { user } = useAuth();
  const canEdit = paper.author?.id === user?.id;

  return (
    <div style={styles.paperCard}>
      <div style={styles.paperHeader}>
        <div style={styles.paperStatus}>
          <span style={{
            ...styles.statusBadge,
            ...styles[`status-${paper.status}`]
          }}>
            {paper.status}
          </span>
        </div>
        {canEdit && (
          <div style={styles.paperActions}>
            <Link to={`/papers/${paper.id}/edit`} style={styles.actionButton}>
              <Edit3 size={16} />
            </Link>
            <button
              onClick={() => onDelete(paper.id)}
              style={{ ...styles.actionButton, color: '#ef4444' }}
              title="Delete paper"
            >
              <Trash2 size={16} />
            </button>
          </div>
        )}
      </div>

      <h3 style={styles.paperTitle}>
        <Link to={`/papers/${paper.id}`} style={styles.paperLink}>
          {paper.title}
        </Link>
      </h3>

      {paper.source_journal && (
        <p style={styles.paperJournal}>
          <FileText size={14} />
          {paper.source_journal}
        </p>
      )}

      {paper.publication_date && (
        <p style={styles.paperDate}>
          <Calendar size={14} />
          {new Date(paper.publication_date).toLocaleDateString()}
        </p>
      )}

      <div style={styles.paperMeta}>
        <span style={styles.paperAuthor}>
          <User size={14} />
          {paper.author?.first_name} {paper.author?.last_name}
        </span>
        {paper.citations > 0 && (
          <span style={styles.paperCitations}>
            <BarChart3 size={14} />
            {paper.citations} citations
          </span>
        )}
      </div>
    </div>
  );
};

const CreatePaperModal = ({ onClose, onCreate }) => {
  const [formData, setFormData] = useState({
    title: '',
    abstract: '',
    source_journal: '',
    status: 'draft'
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onCreate(formData);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={styles.modalOverlay}>
      <div style={styles.modal}>
        <div style={styles.modalHeader}>
          <h3>Create New Paper</h3>
          <button onClick={onClose} style={styles.modalClose}>
            ×
          </button>
        </div>
        <form onSubmit={handleSubmit} style={styles.modalForm}>
          <div style={styles.inputGroup}>
            <label>Title *</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              required
              style={styles.input}
            />
          </div>
          <div style={styles.inputGroup}>
            <label>Journal/Conference</label>
            <input
              type="text"
              value={formData.source_journal}
              onChange={(e) => setFormData(prev => ({ ...prev, source_journal: e.target.value }))}
              style={styles.input}
            />
          </div>
          <div style={styles.inputGroup}>
            <label>Status</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
              style={styles.select}
            >
              <option value="draft">Draft</option>
              <option value="submitted">Submitted</option>
              <option value="published">Published</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          <div style={styles.inputGroup}>
            <label>Abstract</label>
            <textarea
              value={formData.abstract}
              onChange={(e) => setFormData(prev => ({ ...prev, abstract: e.target.value }))}
              rows={4}
              style={styles.textarea}
            />
          </div>
          <div style={styles.modalActions}>
            <button
              type="button"
              onClick={onClose}
              style={styles.cancelButton}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !formData.title}
              style={styles.submitButton}
            >
              {loading ? <Loader2 size={16} style={styles.spinner} /> : 'Create Paper'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const styles = {
  container: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '20px'
  },
  header: {
    marginBottom: '30px'
  },
  headerContent: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: '20px'
  },
  title: {
    fontSize: '2rem',
    fontWeight: '700',
    color: '#1e293b',
    margin: '0 0 8px 0'
  },
  subtitle: {
    fontSize: '1.1rem',
    color: '#64748b',
    margin: '0'
  },
  createButton: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '12px 20px',
    background: '#2563eb',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    fontWeight: '600',
    fontSize: '1rem',
    transition: 'all 0.2s ease',
    ':hover': {
      background: '#1d4ed8'
    }
  },
  controls: {
    display: 'flex',
    gap: '20px',
    marginBottom: '30px',
    alignItems: 'center'
  },
  searchContainer: {
    position: 'relative',
    flex: '1',
    maxWidth: '400px'
  },
  searchIcon: {
    position: 'absolute',
    left: '12px',
    top: '50%',
    transform: 'translateY(-50%)',
    color: '#64748b'
  },
  searchInput: {
    width: '100%',
    padding: '12px 12px 12px 40px',
    border: '1px solid #d1d5db',
    borderRadius: '8px',
    fontSize: '1rem',
    ':focus': {
      outline: 'none',
      borderColor: '#2563eb'
    }
  },
  filterContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  filterSelect: {
    padding: '10px',
    border: '1px solid #d1d5db',
    borderRadius: '6px',
    fontSize: '0.875rem'
  },
  papersGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
    gap: '20px'
  },
  paperCard: {
    background: 'white',
    padding: '20px',
    borderRadius: '12px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e2e8f0',
    transition: 'all 0.2s ease',
    ':hover': {
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      transform: 'translateY(-2px)'
    }
  },
  paperHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '12px'
  },
  paperStatus: {
    flex: '1'
  },
  statusBadge: {
    padding: '4px 12px',
    borderRadius: '16px',
    fontSize: '0.75rem',
    fontWeight: '500'
  },
  'status-draft': { background: '#f1f5f9', color: '#334155' },
  'status-submitted': { background: '#fef3c7', color: '#92400e' },
  'status-published': { background: '#dcfce7', color: '#166534' },
  'status-rejected': { background: '#fef2f2', color: '#dc2626' },
  paperActions: {
    display: 'flex',
    gap: '8px'
  },
  actionButton: {
    padding: '6px',
    border: 'none',
    background: 'none',
    color: '#64748b',
    cursor: 'pointer',
    borderRadius: '4px',
    transition: 'all 0.2s ease',
    ':hover': {
      background: '#f1f5f9',
      color: '#374151'
    }
  },
  paperTitle: {
    margin: '0 0 12px 0',
    fontSize: '1.1rem',
    fontWeight: '600',
    color: '#1e293b',
    lineHeight: '1.4'
  },
  paperLink: {
    color: 'inherit',
    textDecoration: 'none',
    ':hover': {
      color: '#2563eb'
    }
  },
  paperJournal: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    margin: '0 0 8px 0',
    fontSize: '0.875rem',
    color: '#64748b'
  },
  paperDate: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    margin: '0 0 12px 0',
    fontSize: '0.875rem',
    color: '#64748b'
  },
  paperMeta: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: '12px',
    borderTop: '1px solid #f1f5f9'
  },
  paperAuthor: {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    fontSize: '0.75rem',
    color: '#64748b'
  },
  paperCitations: {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    fontSize: '0.75rem',
    color: '#64748b'
  },
  emptyState: {
    gridColumn: '1 / -1',
    textAlign: 'center',
    padding: '60px 20px',
    color: '#64748b'
  },
  errorBanner: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '12px 16px',
    background: '#fef2f2',
    border: '1px solid #fecaca',
    borderRadius: '6px',
    color: '#dc2626',
    marginBottom: '20px',
    fontSize: '0.875rem'
  },
  dismissError: {
    background: 'none',
    border: 'none',
    color: '#dc2626',
    cursor: 'pointer',
    fontSize: '1.2rem',
    padding: '0',
    marginLeft: 'auto'
  },
  modalOverlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000
  },
  modal: {
    background: 'white',
    borderRadius: '12px',
    padding: '0',
    width: '90%',
    maxWidth: '500px',
    maxHeight: '90vh',
    overflow: 'auto'
  },
  modalHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '20px',
    borderBottom: '1px solid #e2e8f0'
  },
  modalClose: {
    background: 'none',
        border: 'none',
        color: '#64748b',
        cursor: 'pointer',
        fontSize: '1.2rem',
        padding: '0'
  },
  modalContent: {
    padding: '20px'
  },
  modalTitle: {
    margin: '0',
    fontSize: '1.2rem',
    fontWeight: '600',
    color: '#1e293b'
  },
  modalForm: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    marginTop: '12px'
  },
  modalInput: {
    padding: '10px',
    border: '1px solid #d1d5db',
    borderRadius: '6px',
    fontSize: '0.875rem'
    } 
  };

export default PapersListPage;
    