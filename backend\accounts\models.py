# accounts/models.py

import uuid
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta

def get_invitation_expiry():
    """Returns the default expiry time for invitations (7 days from now)."""
    return timezone.now() + timedelta(days=7)


# ==============================================================================
# 1. CORE INSTITUTIONAL STRUCTURE (The "Where")
# ==============================================================================

class Institution(models.Model):
    """LEVEL 1: The University (Top-level tenant and paying customer)."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.name

class Department(models.Model):
    """LEVEL 2: A School or Faculty within the Institution."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    institution = models.ForeignKey(Institution, on_delete=models.CASCADE, related_name='departments')
    
    def __str__(self):
        return f"{self.name}, {self.institution.name}"
    class Meta:
        unique_together = ('institution', 'name')

class ResearchGroup(models.Model):
    """LEVEL 3: A specific Lab or Unit within a Department."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='research_groups')
    
    def __str__(self):
        return f"{self.name}, {self.department.name}"
    class Meta:
        unique_together = ('department', 'name')



# ==============================================================================
# 2. PEOPLE AND THEIR ROLES (The "Who" and "What")
# ==============================================================================

class RoleType(models.Model):
    """Defines the types of roles available within an institution."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    can_supervise = models.BooleanField(default=False, help_text="Can this role be a Primary or Co-Supervisor?")
    can_invite = models.BooleanField(default=False, help_text="Can this role invite others to a Research Group?")

    def __str__(self):
        return self.name

class CustomUser(AbstractUser):
    """Represents a unique Person in the system."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # Use string references for models defined later
    primary_role = models.ForeignKey(
        'RoleType',  # Changed to string reference
        on_delete=models.SET_NULL,
        related_name='primary_users',
        null=True, blank=True
    )
    primary_department = models.ForeignKey(
        'Department',  # Changed to string reference
        on_delete=models.SET_NULL,
        related_name='staff_and_students',
        null=True, blank=True
    )
    profile_picture = models.ImageField(upload_to='profile_pictures/', null=True, blank=True)
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def __str__(self):
        return self.get_full_name() or self.username

class UserRole(models.Model):
    """Junction table linking a User to a RoleType within a specific Department."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='roles')
    role_type = models.ForeignKey(RoleType, on_delete=models.CASCADE, related_name='user_roles')
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='user_roles')
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField(null=True, blank=True)
    is_primary = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    def clean(self):
        if self.is_primary:
            existing_primary = UserRole.objects.filter(user=self.user, is_primary=True).exclude(pk=self.pk)
            if existing_primary.exists():
                raise ValidationError("A user can only have one primary role.")
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} - {self.role_type.name} at {self.department.name}"


# ==============================================================================
# 3. RESEARCH GROUP MEMBERSHIP (The "Team")
# ==============================================================================

class ResearchGroupMembership(models.Model):
    """Junction table linking a User to a Research Group, capturing the invitation."""
    class Status(models.TextChoices):
        INVITED = 'INVITED', 'Invited'
        ACTIVE = 'ACTIVE', 'Active'
        INACTIVE = 'INACTIVE', 'Inactive'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    person = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='group_memberships')
    research_group = models.ForeignKey(ResearchGroup, on_delete=models.CASCADE, related_name='memberships')
    invited_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, related_name='group_memberships_initiated', null=True, blank=True)
    status = models.CharField(max_length=10, choices=Status.choices, default=Status.INVITED)
    date_joined = models.DateTimeField(null=True, blank=True)
    date_left = models.DateTimeField(null=True, blank=True)

    def clean(self):
        if self.invited_by:
            if not self.invited_by.roles.filter(role_type__can_invite=True, is_active=True).exists():
                raise ValidationError(f"{self.invited_by.username} does not have permission to invite members.")
    
    def save(self, *args, **kwargs):
        if self.status == self.Status.ACTIVE and not self.date_joined:
            self.date_joined = timezone.now()
        if self.status == self.Status.INACTIVE and not self.date_left:
            self.date_left = timezone.now()
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.person.username} in {self.research_group.name}"
    class Meta:
        unique_together = ('person', 'research_group')


# ==============================================================================
# 4. THE INVITATION SYSTEM
# ==============================================================================

import uuid
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.conf import settings
from django.utils import timezone
from datetime import timedelta

# Helper function for the expiry date
def get_invitation_expiry_date():
    """Returns a datetime object for 30 days in the future."""
    return timezone.now() + timedelta(days=30)


class Invitation(models.Model):
    """
    Tracks a specific, one-time invitation sent by a user with invite permissions
    to a new potential member for a specific role and research group.
    """
    class Status(models.TextChoices):
        PENDING = 'PENDING', 'Pending'
        ACCEPTED = 'ACCEPTED', 'Accepted'
        EXPIRED = 'EXPIRED', 'Expired'
        REVOKED = 'REVOKED', 'Revoked'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    
    # --- FIXED: Only one invitee_email field ---
    invitee_email = models.EmailField(
        help_text="The email address of the person being invited."
    )
    
    # The role the new user will be granted upon accepting the invitation
    intended_role = models.ForeignKey(
        RoleType, 
        on_delete=models.CASCADE,
        help_text="The role the invitee will have upon joining."
    )
    
    status = models.CharField(
        max_length=20, 
        choices=Status.choices, 
        default=Status.PENDING
    )
    
    # The user who sent the invitation
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_invitations',
    )
    
    # The research group the invitee will join
    research_group = models.ForeignKey(
        ResearchGroup, 
        on_delete=models.CASCADE,
        related_name='invitations'
    )
    
    # The new user account that is created when the invitation is accepted
    invitee = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='invitation_used', # <-- Renamed for clarity
        null=True, blank=True
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    # --- FIXED: Uses the helper function defined above ---
    expires_at = models.DateTimeField(default=get_invitation_expiry)
    accepted_at = models.DateTimeField(null=True, blank=True)
    
    def is_valid(self):
        """Checks if the invitation is still valid (not expired and pending)."""
        return self.status == self.Status.PENDING and timezone.now() < self.expires_at

    def clean(self):
        """
        Consolidated validation logic to enforce business rules before saving.
        """
        if not self.invited_by_id: # Check if inviter is set
            return

        # --- CONSOLIDATED & SIMPLIFIED PERMISSION CHECK ---
        # The inviter must have an active role with invite permissions AND
        # be an active member of the group they are inviting to.
        can_invite_from_group = self.invited_by.roles.filter(
            role_type__can_invite=True,
            is_active=True,
            department=self.research_group.department # Role must be in the correct department
        ).exists()

        is_active_member = self.invited_by.group_memberships.filter(
            research_group=self.research_group,
            status=ResearchGroupMembership.Status.ACTIVE
        ).exists()

        if not (can_invite_from_group and is_active_member):
            raise ValidationError(
                "The inviting user does not have permission to invite members to this research group."
            )
                
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Invitation for {self.invitee_email} to join {self.research_group.name} ({self.get_status_display()})"



# class Invitation(models.Model):
#     """
#     Tracks a specific, one-time invitation sent by a user with invite permissions
#     to a new potential member for a specific role.
#     """
#     class Status(models.TextChoices):
#         PENDING = 'PENDING', 'Pending'
#         ACCEPTED = 'ACCEPTED', 'Accepted'
#         EXPIRED = 'EXPIRED', 'Expired'
#         REVOKED = 'REVOKED', 'Revoked'

#     id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
#     # Use 'code' instead of 'token' for clarity and consistency with registration flow
#     code = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    
#     invitee_email = models.EmailField()
    
#     # The role the new user will be granted upon accepting the invitation
#     intended_role = models.ForeignKey(RoleType, on_delete=models.CASCADE)
    
#     status = models.CharField(max_length=20, choices=Status.choices, default=Status.PENDING)
    
#     # The user who sent the invitation
#     invited_by = models.ForeignKey(
#         CustomUser,
#         on_delete=models.CASCADE,
#         related_name='sent_invitations',
#         limit_choices_to={'primary_role__can_invite': True} # Better way to check
#     )
    
#     # The research group the invitee will join
#     research_group = models.ForeignKey(
#         ResearchGroup, 
#         on_delete=models.CASCADE, # If group is deleted, its pending invitations are also deleted
#         related_name='invitations'
#     )
    
#     invitee_email = models.EmailField()
    
#     # The new user account that is created when the invitation is accepted
#     invitee = models.OneToOneField(
#         CustomUser,
#         on_delete=models.SET_NULL,
#         related_name='accepted_invitation',
#         null=True, blank=True
#     )
    
#     created_at = models.DateTimeField(auto_now_add=True)
#     expires_at = models.DateTimeField(default=get_invitation_expiry)
#     accepted_at = models.DateTimeField(null=True, blank=True)
    
#     def is_valid(self):
#         """Checks if the invitation is still valid (not expired and pending)."""
#         return self.status == self.Status.PENDING and timezone.now() < self.expires_at

#     def clean(self):
#         # Enforce business logic before saving
#         if self.invited_by:
#             # Check if the inviter is actually a member of the research group they're inviting to
#             if not self.invited_by.group_memberships.filter(
#                 research_group=self.research_group, status='ACTIVE'
#             ).exists():
#                 raise ValidationError("Inviter must be an active member of the specified research group.")
            
#             # Check if the inviter has the permission to invite
#             if not self.invited_by.roles.filter(role_type__can_invite=True, is_active=True).exists():
#                 raise ValidationError("The inviting user does not have permission to send invitations.")
                
#     def save(self, *args, **kwargs):
#         self.clean()
#         super().save(*args, **kwargs)

#     def __str__(self):
#         return f"Invitation for {self.invitee_email} to join {self.research_group.name} ({self.get_status_display()})"
