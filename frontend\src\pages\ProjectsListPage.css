.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-header h1 {
    margin: 0;
    color: var(--dark);
    font-size: 2.5rem;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary);
    text-decoration: underline;
    cursor: pointer;
    margin-left: 0.5rem;
}

.error-alert {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 2rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.project-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s, box-shadow 0.2s;
    overflow: hidden;
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.project-link {
    display: block;
    padding: 1.5rem;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.project-header h3 {
    margin: 0;
    color: var(--primary);
    font-size: 1.25rem;
    flex: 1;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.status-draft {
    background-color: #fff0c2;
    color: #b38f00;
}

.status-in-progress {
    background-color: #c8e6c9;
    color: #2e7d32;
}

.status-completed {
    background-color: #e0e0e0;
    color: #424242;
}

.status-published {
    background-color: #bbdefb;
    color: #1565c0;
}

.project-details {
    margin-top: 1rem;
}

.project-details p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.label {
    font-weight: 600;
    color: var(--gray);
    margin-right: 0.25rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 2rem 0;
}

.empty-state h2 {
    color: var(--dark);
    margin-bottom: 1rem;
}

.empty-state p {
    color: var(--gray);
    margin-bottom: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
}


