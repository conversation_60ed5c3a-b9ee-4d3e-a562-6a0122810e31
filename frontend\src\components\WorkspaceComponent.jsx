import React, { useState, useEffect } from "react";
import { 
  FileText, 
  MessageSquare, 
  Edit3, 
  Plus, 
  Search,
  Filter,
  Calendar,
  User,
  Send,
  Loader2
} from "lucide-react";
import TipTapEditor from "./TiptapEditor";
import { 
  getProjects, 
  createProject, 
  getPapers,
  createPaper,
  getProjectMessages,
  sendProjectMessage 
} from "../services/api";
import { useAuth } from "../context/AuthContext";
import { manuscriptTemplate } from "../utils/manuscriptTemplate";

const WorkspaceComponent = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("writing");
  const [projects, setProjects] = useState([]);
  const [papers, setPapers] = useState([]);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Writing workspace state
  const [editorContent, setEditorContent] = useState("");
  const [documentTitle, setDocumentTitle] = useState("Untitled Document");

  // Chat state
  const [newMessage, setNewMessage] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);

  // Manuscript state
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    if (activeTab === "manuscripts") {
      fetchPapers();
    } else if (activeTab === "projects") {
      fetchProjects();
    }
  }, [activeTab]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await getProjects();
      const projectsData = response.data.results || response.data;
      setProjects(Array.isArray(projectsData) ? projectsData : []);
    } catch (err) {
      setError("Failed to load projects");
    } finally {
      setLoading(false);
    }
  };

  const fetchPapers = async () => {
    try {
      setLoading(true);
      const response = await getPapers();
      const papersData = response.data.results || response.data;
      setPapers(Array.isArray(papersData) ? papersData : []);
    } catch (err) {
      setError("Failed to load manuscripts");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProject = async () => {
    try {
      const newProject = {
        title: "New Research Project",
        description: "A new research project",
        content: JSON.stringify(manuscriptTemplate)
      };
      await createProject(newProject);
      fetchProjects();
    } catch (err) {
      setError("Failed to create project");
    }
  };

  const handleCreatePaper = async () => {
    try {
      const newPaper = {
        title: "New Manuscript",
        abstract: "Abstract for new manuscript",
        status: "DRAFT"
      };
      await createPaper(newPaper);
      fetchPapers();
      setShowCreateModal(false);
    } catch (err) {
      setError("Failed to create manuscript");
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    setSendingMessage(true);
    try {
      // This would need to be connected to a specific project or general chat
      // For now, we'll simulate adding a message
      const tempMessage = {
        id: Date.now(),
        content: newMessage,
        author: user,
        created_at: new Date().toISOString()
      };
      setMessages(prev => [...prev, tempMessage]);
      setNewMessage("");
    } catch (err) {
      setError("Failed to send message");
    } finally {
      setSendingMessage(false);
    }
  };

  const renderWritingWorkspace = () => (
    <div style={styles.workspaceContent}>
      <div style={styles.documentHeader}>
        <input
          type="text"
          value={documentTitle}
          onChange={(e) => setDocumentTitle(e.target.value)}
          style={styles.titleInput}
          placeholder="Document Title"
        />
        <button style={styles.saveButton}>
          Save Document
        </button>
      </div>
      <div style={styles.editorContainer}>
        <TipTapEditor
          content={editorContent}
          onChange={setEditorContent}
          placeholder="Start writing your research document..."
        />
      </div>
    </div>
  );

  const renderChatWorkspace = () => (
    <div style={styles.workspaceContent}>
      <div style={styles.chatHeader}>
        <h3>Research Group Chat</h3>
      </div>
      <div style={styles.chatMessages}>
        {messages.length === 0 ? (
          <div style={styles.emptyChat}>
            <MessageSquare size={48} />
            <p>No messages yet</p>
            <p style={styles.emptyChatSubtitle}>Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} style={styles.message}>
              <div style={styles.messageHeader}>
                <strong>{message.author?.username || 'Unknown User'}</strong>
                <span style={styles.messageTime}>
                  {new Date(message.created_at).toLocaleTimeString()}
                </span>
              </div>
              <p style={styles.messageContent}>{message.content}</p>
            </div>
          ))
        )}
      </div>
      <form onSubmit={handleSendMessage} style={styles.chatForm}>
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type a message..."
          disabled={sendingMessage}
          style={styles.chatInput}
        />
        <button 
          type="submit" 
          disabled={!newMessage.trim() || sendingMessage} 
          style={styles.chatButton}
        >
          {sendingMessage ? <Loader2 size={16} /> : <Send size={16} />}
        </button>
      </form>
    </div>
  );

  const renderManuscriptsWorkspace = () => (
    <div style={styles.workspaceContent}>
      <div style={styles.manuscriptHeader}>
        <div style={styles.searchSection}>
          <div style={styles.searchContainer}>
            <Search size={16} style={styles.searchIcon} />
            <input
              type="text"
              placeholder="Search manuscripts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={styles.searchInput}
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            style={styles.filterSelect}
          >
            <option value="all">All Status</option>
            <option value="DRAFT">Draft</option>
            <option value="REVIEW">In Review</option>
            <option value="SUBMITTED">Submitted</option>
            <option value="PUBLISHED">Published</option>
          </select>
        </div>
        <button onClick={() => setShowCreateModal(true)} style={styles.createButton}>
          <Plus size={16} />
          New Manuscript
        </button>
      </div>

      {loading ? (
        <div style={styles.loadingContainer}>
          <Loader2 size={24} style={styles.spinning} />
          <p>Loading manuscripts...</p>
        </div>
      ) : (
        <div style={styles.manuscriptGrid}>
          {papers.map((paper) => (
            <div key={paper.id} style={styles.manuscriptCard}>
              <div style={styles.cardHeader}>
                <FileText size={20} />
                <span style={{
                  ...styles.statusBadge,
                  backgroundColor: getStatusColor(paper.status)
                }}>
                  {paper.status}
                </span>
              </div>
              <h4 style={styles.cardTitle}>{paper.title}</h4>
              <p style={styles.cardDescription}>{paper.abstract}</p>
              <div style={styles.cardFooter}>
                <span style={styles.cardDate}>
                  <Calendar size={14} />
                  {new Date(paper.created_at).toLocaleDateString()}
                </span>
                <button style={styles.editButton}>
                  <Edit3 size={14} />
                  Edit
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {showCreateModal && (
        <div style={styles.modal}>
          <div style={styles.modalContent}>
            <h3>Create New Manuscript</h3>
            <p>This will create a new manuscript with a basic template.</p>
            <div style={styles.modalActions}>
              <button onClick={() => setShowCreateModal(false)} style={styles.cancelButton}>
                Cancel
              </button>
              <button onClick={handleCreatePaper} style={styles.confirmButton}>
                Create Manuscript
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const getStatusColor = (status) => {
    const colors = {
      DRAFT: "#f59e0b",
      REVIEW: "#3b82f6",
      SUBMITTED: "#8b5cf6",
      PUBLISHED: "#10b981"
    };
    return colors[status] || "#6b7280";
  };

  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h2>Research Workspace</h2>
        <div style={styles.tabs}>
          <button
            onClick={() => setActiveTab("writing")}
            style={{
              ...styles.tab,
              ...(activeTab === "writing" && styles.tabActive)
            }}
          >
            <Edit3 size={16} />
            Writing
          </button>
          <button
            onClick={() => setActiveTab("chat")}
            style={{
              ...styles.tab,
              ...(activeTab === "chat" && styles.tabActive)
            }}
          >
            <MessageSquare size={16} />
            Chat
          </button>
          <button
            onClick={() => setActiveTab("manuscripts")}
            style={{
              ...styles.tab,
              ...(activeTab === "manuscripts" && styles.tabActive)
            }}
          >
            <FileText size={16} />
            Manuscripts
          </button>
        </div>
      </div>

      {error && (
        <div style={styles.errorMessage}>
          {error}
        </div>
      )}

      <div style={styles.workspace}>
        {activeTab === "writing" && renderWritingWorkspace()}
        {activeTab === "chat" && renderChatWorkspace()}
        {activeTab === "manuscripts" && renderManuscriptsWorkspace()}
      </div>
    </div>
  );
};

const styles = {
  container: {
    background: "white",
    borderRadius: "8px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
    overflow: "hidden",
    margin: "20px 0"
  },
  header: {
    padding: "20px",
    borderBottom: "1px solid #e2e8f0",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center"
  },
  tabs: {
    display: "flex",
    gap: "8px"
  },
  tab: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "8px 16px",
    border: "1px solid #e2e8f0",
    background: "white",
    color: "#64748b",
    borderRadius: "6px",
    cursor: "pointer",
    transition: "all 0.2s ease",
    fontSize: "14px",
    fontWeight: "500"
  },
  tabActive: {
    background: "#14b8a6",
    color: "white",
    borderColor: "#14b8a6"
  },
  workspace: {
    minHeight: "500px"
  },
  workspaceContent: {
    padding: "20px",
    height: "100%"
  },
  errorMessage: {
    background: "#fee2e2",
    color: "#dc2626",
    padding: "12px 20px",
    borderBottom: "1px solid #e2e8f0"
  },
  // Writing workspace styles
  documentHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "20px",
    paddingBottom: "15px",
    borderBottom: "1px solid #e2e8f0"
  },
  titleInput: {
    fontSize: "1.5rem",
    fontWeight: "600",
    border: "none",
    outline: "none",
    background: "transparent",
    flex: 1,
    marginRight: "20px"
  },
  saveButton: {
    background: "#14b8a6",
    color: "white",
    border: "none",
    padding: "8px 16px",
    borderRadius: "6px",
    cursor: "pointer"
  },
  editorContainer: {
    minHeight: "400px"
  },
  // Chat workspace styles
  chatHeader: {
    marginBottom: "20px",
    paddingBottom: "15px",
    borderBottom: "1px solid #e2e8f0"
  },
  chatMessages: {
    height: "350px",
    overflowY: "auto",
    marginBottom: "20px",
    padding: "10px",
    border: "1px solid #e2e8f0",
    borderRadius: "8px"
  },
  emptyChat: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
    color: "#64748b"
  },
  emptyChatSubtitle: {
    fontSize: "0.875rem",
    marginTop: "8px"
  },
  message: {
    marginBottom: "16px",
    padding: "12px",
    background: "#f8fafc",
    borderRadius: "8px"
  },
  messageHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "8px"
  },
  messageTime: {
    fontSize: "0.75rem",
    color: "#64748b"
  },
  messageContent: {
    margin: 0,
    fontSize: "0.875rem"
  },
  chatForm: {
    display: "flex",
    gap: "8px"
  },
  chatInput: {
    flex: 1,
    padding: "8px 12px",
    border: "1px solid #e2e8f0",
    borderRadius: "6px",
    fontSize: "0.875rem"
  },
  chatButton: {
    padding: "8px 12px",
    border: "none",
    background: "#14b8a6",
    color: "white",
    borderRadius: "6px",
    cursor: "pointer",
    display: "flex",
    alignItems: "center"
  },
  // Manuscripts workspace styles
  manuscriptHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "20px"
  },
  searchSection: {
    display: "flex",
    gap: "12px",
    alignItems: "center"
  },
  searchContainer: {
    position: "relative",
    display: "flex",
    alignItems: "center"
  },
  searchIcon: {
    position: "absolute",
    left: "8px",
    color: "#64748b"
  },
  searchInput: {
    paddingLeft: "32px",
    padding: "8px 12px",
    border: "1px solid #e2e8f0",
    borderRadius: "6px",
    fontSize: "0.875rem",
    width: "250px"
  },
  filterSelect: {
    padding: "8px 12px",
    border: "1px solid #e2e8f0",
    borderRadius: "6px",
    fontSize: "0.875rem"
  },
  createButton: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    background: "#14b8a6",
    color: "white",
    border: "none",
    padding: "8px 16px",
    borderRadius: "6px",
    cursor: "pointer"
  },
  loadingContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "200px",
    color: "#64748b"
  },
  spinning: {
    animation: "spin 1s linear infinite"
  },
  manuscriptGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))",
    gap: "20px"
  },
  manuscriptCard: {
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    padding: "16px",
    background: "white",
    transition: "box-shadow 0.2s ease"
  },
  cardHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "12px"
  },
  statusBadge: {
    padding: "4px 8px",
    borderRadius: "4px",
    fontSize: "0.75rem",
    fontWeight: "500",
    color: "white",
    textTransform: "uppercase"
  },
  cardTitle: {
    margin: "0 0 8px 0",
    fontSize: "1.1rem",
    fontWeight: "600"
  },
  cardDescription: {
    margin: "0 0 16px 0",
    fontSize: "0.875rem",
    color: "#64748b",
    lineHeight: "1.4"
  },
  cardFooter: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center"
  },
  cardDate: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    fontSize: "0.75rem",
    color: "#64748b"
  },
  editButton: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    background: "none",
    border: "1px solid #e2e8f0",
    padding: "4px 8px",
    borderRadius: "4px",
    cursor: "pointer",
    fontSize: "0.75rem"
  },
  // Modal styles
  modal: {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: "rgba(0, 0, 0, 0.5)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1000
  },
  modalContent: {
    background: "white",
    padding: "24px",
    borderRadius: "8px",
    maxWidth: "400px",
    width: "90%"
  },
  modalActions: {
    display: "flex",
    gap: "12px",
    justifyContent: "flex-end",
    marginTop: "20px"
  },
  cancelButton: {
    background: "none",
    border: "1px solid #e2e8f0",
    padding: "8px 16px",
    borderRadius: "6px",
    cursor: "pointer"
  },
  confirmButton: {
    background: "#14b8a6",
    color: "white",
    border: "none",
    padding: "8px 16px",
    borderRadius: "6px",
    cursor: "pointer"
  }
};

export default WorkspaceComponent;
