from rest_framework import permissions

class IsGroupAdmin(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        return obj.can_user_manage(request.user)


class CanInviteMembers(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True

        # Check if user is authenticated
        if not request.user or not request.user.is_authenticated:
            return False

        # Check if the user has ANY active role that has the 'can_invite' permission flag
        from .models import UserRole
        return UserRole.objects.filter(
            user=request.user,
            role_type__can_invite=True,
            is_active=True
        ).exists()



# backend/accounts/permissions.py

from rest_framework import permissions
from .models import CustomUser, UserRole

class IsWorkspaceAdmin(permissions.BasePermission):
    """
    Custom permission to only allow users who have a role with
    administrative or supervisory capabilities.
    
    This checks the 'can_supervise' or 'can_invite' flags on the user's RoleType.
    """
    def has_permission(self, request, view):
        # First, ensure the user is authenticated.
        if not request.user or not request.user.is_authenticated:
            return False

        # Check if the user has any active role that grants them admin-like permissions.
        # This is much more flexible than just checking for a role named "ADMIN".
        return UserRole.objects.filter(
            user=request.user,
            is_active=True,
            role_type__can_supervise=True # Or you could check for can_invite
        ).exists()



from rest_framework import permissions
from .models import UserRole # We need UserRole to check permissions

class IsInstitutionAdmin(permissions.BasePermission):
    """
    Custom permission to only allow users who are Institution Administrators.
    """
    def has_permission(self, request, view):
        # First, ensure the user is authenticated.
        if not request.user or not request.user.is_authenticated:
            return False
        # Now, check if this user has an active role of type 'Institution Administrator'.
        return UserRole.objects.filter(
            user=request.user, 
            role_type__name='Institution Administrator', 
            is_active=True
        ).exists()


class IsDepartmentAdmin(permissions.BasePermission):
    """
    Custom permission for users who can manage a Department (e.g., a Dean).
    """
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        # Check for roles that have department-level management permissions.
        return UserRole.objects.filter(
            user=request.user, 
            role_type__name__in=['Dean', 'Head of School'], 
            is_active=True
        ).exists()


class IsGroupAdmin(permissions.BasePermission):
    """
    Custom permission for users who can manage a Research Group (e.g., a Professor).
    Checks the 'can_invite' flag on their RoleType.
    """
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        # Check if the user has ANY active role that has the 'can_invite' permission flag.
        return UserRole.objects.filter(
            user=request.user, 
            role_type__can_invite=True, 
            is_active=True
        ).exists()
        



