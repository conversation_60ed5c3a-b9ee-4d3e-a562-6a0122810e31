// frontend/src/components/ProtectedRoute.jsx

import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const ProtectedRoute = ({ children }) => {
  const { userToken, loading } = useAuth(); // Get the token and loading state from our context
  const location = useLocation();

  // While the AuthContext is performing its initial check for a token
  // in localStorage, we don't render anything. This prevents a "flash" of the
  // login page for already logged-in users.
  if (loading) {
    return null; // Or you can return a full-page loading spinner here
  }

  // After loading, if there's no token, redirect to the login page.
  if (!userToken) {
    // We pass the original location the user was trying to access in the state.
    // This allows us to redirect them back to that page after they successfully log in.
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If loading is finished and there is a token, render the child component
  // (e.g., the DashboardPage).
  return children;
};

export default ProtectedRoute;



