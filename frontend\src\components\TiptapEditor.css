.tiptap-editor {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-right: 0.75rem;
  border-right: 1px solid #e2e8f0;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #64748b;
  transition: all 0.2s;
}

.toolbar-button:hover:not(:disabled) {
  background-color: #fff;
  border-color: #e2e8f0;
  color: #334155;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button.active {
  background-color: #14b8a6;
  color: white;
  border-color: #14b8a6;
}

/* Image Dialog Styles */
.image-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.image-dialog {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
}

.image-dialog h3 {
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.image-url-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 16px;
}

.image-dialog-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancel-button {
  background: none;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.insert-button {
  background: #14b8a6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.insert-button:disabled {
  background: #94a3b8;
  cursor: not-allowed;
}

/* Table Styles */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 16px 0;
  overflow: hidden;
}

.ProseMirror table td,
.ProseMirror table th {
  min-width: 1em;
  border: 2px solid #e2e8f0;
  padding: 8px 12px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background-color: #f8fafc;
}

.ProseMirror table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(20, 184, 166, 0.1);
  pointer-events: none;
}

.ProseMirror table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #14b8a6;
  pointer-events: none;
}

/* Image Styles */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

.ProseMirror img.ProseMirror-selectednode {
  outline: 2px solid #14b8a6;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button.active {
  background-color: #e2e8f0;
  color: #334155;
}

.toolbar-select {
  padding: 0.35rem 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #334155;
  cursor: pointer;
}

.toolbar-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.editor-container {
  padding: 1rem;
  min-height: 300px;
  max-height: 70vh;
  overflow-y: auto;
}

.editor-content {
  outline: none;
  min-height: 250px;
}

.editor-content h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem;
  color: #1e293b;
}

.editor-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem;
  color: #1e293b;
}

.editor-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem;
  color: #1e293b;
}

.editor-content p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #334155;
}

.editor-content ul, 
.editor-content ol {
  padding-left: 1.5rem;
  margin: 0.75rem 0;
}

.editor-content ul {
  list-style-type: disc;
}

.editor-content ol {
  list-style-type: decimal;
}

.editor-content li {
  margin: 0.25rem 0;
}

.editor-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #64748b;
  font-style: italic;
}

.editor-content hr {
  border: none;
  border-top: 1px solid #e2e8f0;
  margin: 1.5rem 0;
}

.editor-content .editor-link {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.editor-content .editor-link:hover {
  color: #2563eb;
}

.editor-content .is-empty::before {
  content: attr(data-placeholder);
  color: #94a3b8;
  float: left;
  height: 0;
  pointer-events: none;
}

.editor-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid #e2e8f0;
  background-color: #f8fafc;
  display: flex;
  justify-content: flex-end;
}

.character-count {
  font-size: 0.75rem;
  color: #64748b;
}

/* Responsive design */
@media (max-width: 768px) {
  .editor-toolbar {
    gap: 0.25rem;
    padding: 0.5rem;
  }
  
  .toolbar-group {
    padding-right: 0.5rem;
  }
  
  .toolbar-button {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .toolbar-select {
    padding: 0.25rem;
    font-size: 12px;
  }
  
  .editor-container {
    padding: 0.75rem;
    min-height: 250px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tiptap-editor {
    border-color: #374151;
    background-color: #1f2937;
  }
  
  .editor-toolbar {
    border-color: #374151;
    background-color: #111827;
  }
  
  .toolbar-button {
    color: #9ca3af;
  }
  
  .toolbar-button:hover:not(:disabled) {
    background-color: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }
  
  .toolbar-button.active {
    background-color: #374151;
    color: #f3f4f6;
  }
  
  .toolbar-select {
    border-color: #374151;
    background-color: #1f2937;
    color: #f3f4f6;
  }
  
  .editor-content h1,
  .editor-content h2,
  .editor-content h3 {
    color: #f9fafb;
  }
  
  .editor-content p {
    color: #d1d5db;
  }
  
  .editor-content blockquote {
    border-color: #374151;
    color: #9ca3af;
  }
  
  .editor-footer {
    border-color: #374151;
    background-color: #111827;
  }
  
  .character-count {
    color: #9ca3af;
  }
}


