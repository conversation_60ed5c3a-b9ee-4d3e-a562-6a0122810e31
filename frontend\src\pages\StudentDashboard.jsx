// frontend/src/pages/StudentDashboard.jsx

import React, { useState, useEffect } from 'react';
import { getStudentDashboardData } from '../services/api';
import { Link } from 'react-router-dom';
import { Briefcase, BarChart3, BookOpen, RefreshCw } from 'lucide-react';
import WorkspaceComponent from '../components/WorkspaceComponent';
import { useAuth } from '../context/AuthContext';

const StudentDashboard = () => {
    const { user } = useAuth();
    const [dashboardData, setDashboardData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [activeTab, setActiveTab] = useState("workspace");

    useEffect(() => {
        if (activeTab === "dashboard") {
            fetchDashboardData();
        }
    }, [activeTab]);

    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            const response = await getStudentDashboardData();
            setDashboardData(response.data);
            setError('');
        } catch (err) {
            console.error("Failed to fetch student dashboard:", err);
            setError("Could not load your dashboard.");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div style={styles.container}>
            <div style={styles.header}>
                <div style={styles.headerContent}>
                    <h1 style={styles.title}>Student Dashboard</h1>
                    <p style={styles.subtitle}>Welcome back, {user?.first_name || user?.username}!</p>
                </div>
                <div style={styles.headerActions}>
                    <button onClick={fetchDashboardData} style={styles.refreshButton} title="Refresh Data">
                        <RefreshCw size={16} />
                    </button>
                </div>
            </div>

            <div style={styles.tabs}>
                <button
                    onClick={() => setActiveTab("workspace")}
                    style={activeTab === "workspace" ? styles.tabActive : styles.tab}
                >
                    <Briefcase size={16} /> Workspace
                </button>
                <button
                    onClick={() => setActiveTab("dashboard")}
                    style={activeTab === "dashboard" ? styles.tabActive : styles.tab}
                >
                    <BarChart3 size={16} /> Overview
                </button>
                <button
                    onClick={() => setActiveTab("library")}
                    style={activeTab === "library" ? styles.tabActive : styles.tab}
                >
                    <BookOpen size={16} /> Research Library
                </button>
            </div>

            {error && (
                <div style={styles.errorMessage}>
                    {error}
                </div>
            )}

            {activeTab === "workspace" && (
                <WorkspaceComponent />
            )}

            {activeTab === "dashboard" && (
                <div style={styles.dashboardContent}>
                    {loading ? (
                        <div style={styles.loadingContainer}>
                            <h2>Loading Your Dashboard...</h2>
                        </div>
                    ) : (
                        <div style={styles.grid}>
                            <div style={styles.card}>
                                <h2>Active Projects</h2>
                                {dashboardData?.active_projects?.length > 0 ? (
                                    dashboardData.active_projects.map(project => (
                                        <div key={project.id} style={styles.item}>
                                            <strong>{project.title}</strong>
                                        </div>
                                    ))
                                ) : (
                                    <p>You are not yet a participant in any active projects.</p>
                                )}
                            </div>

                            <div style={styles.card}>
                                <h2>Recent Papers in Your Feed</h2>
                                {dashboardData?.recent_papers?.length > 0 ? (
                                    dashboardData.recent_papers.map(paper => (
                                        <div key={paper.id} style={styles.item}>
                                            <strong>{paper.title}</strong>
                                            <p style={{margin: '4px 0 0', color: '#64748b', fontSize: '0.9rem'}}>
                                                From: {paper.source_journal}
                                            </p>
                                        </div>
                                    ))
                                ) : (
                                    <p>Your automated research feed is empty. Set your keywords to get started!</p>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            )}

            {activeTab === "library" && (
                <div style={styles.card}>
                    <h2>Research Library</h2>
                    <p>Your personal research library and saved papers will appear here.</p>
                    <p style={{color: '#64748b', fontSize: '0.9rem'}}>
                        This feature is coming soon! You'll be able to save, organize, and annotate research papers.
                    </p>
                </div>
            )}
        </div>
    );
};

const styles = {
    container: {
        maxWidth: "1200px",
        margin: "20px auto",
        fontFamily: "system-ui, sans-serif",
        padding: "0 20px"
    },
    header: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: "30px",
        padding: "20px 0",
        borderBottom: "1px solid #e2e8f0"
    },
    headerContent: {
        flex: 1
    },
    title: {
        margin: "0 0 8px 0",
        fontSize: "2rem",
        fontWeight: "600",
        color: "#1e293b"
    },
    subtitle: {
        margin: 0,
        color: "#64748b",
        fontSize: "1.1rem"
    },
    headerActions: {
        display: "flex",
        gap: "12px"
    },
    refreshButton: {
        background: "#14b8a6",
        color: "white",
        border: "none",
        padding: "8px",
        borderRadius: "6px",
        cursor: "pointer",
        display: "flex",
        alignItems: "center",
        transition: "background-color 0.2s ease"
    },
    tabs: {
        display: "flex",
        gap: "8px",
        marginBottom: "20px",
        borderBottom: "1px solid #e2e8f0",
        paddingBottom: "0"
    },
    tab: {
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "12px 24px",
        border: "none",
        background: "none",
        color: "#64748b",
        cursor: "pointer",
        fontSize: "0.875rem",
        fontWeight: "500",
        transition: "all 0.2s ease",
        borderBottom: "2px solid transparent"
    },
    tabActive: {
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "12px 24px",
        border: "none",
        background: "none",
        color: "#14b8a6",
        cursor: "pointer",
        fontSize: "0.875rem",
        fontWeight: "500",
        transition: "all 0.2s ease",
        borderBottom: "2px solid #14b8a6"
    },
    errorMessage: {
        background: "#fee2e2",
        color: "#dc2626",
        padding: "12px 20px",
        borderRadius: "6px",
        marginBottom: "20px"
    },
    dashboardContent: {
        marginTop: "20px"
    },
    loadingContainer: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "200px",
        color: "#64748b"
    },
    grid: {
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '30px',
        marginTop: '20px'
    },
    card: {
        background: 'white',
        padding: '25px',
        borderRadius: '8px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0'
    },
    item: {
        padding: '15px 0',
        borderBottom: '1px solid #f1f5f9'
    }
};

export default StudentDashboard;



