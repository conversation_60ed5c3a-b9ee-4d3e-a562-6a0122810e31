/* AdminDashboard.css */

.admin-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.admin-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.admin-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #fef3c7;
  color: #92400e;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  gap: 16px;
}

.admin-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.admin-error {
  padding: 16px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  margin-bottom: 24px;
}

.admin-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background-color: white;
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-tab {
  padding: 12px 24px;
  background: none;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-tab.active {
  background-color: #3b82f6;
  color: white;
}

.admin-tab:hover:not(.active) {
  background-color: #f1f5f9;
  color: #374151;
}

.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.admin-stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: #eff6ff;
  color: #3b82f6;
  border-radius: 8px;
}

.admin-stat-content {
  flex: 1;
}

.admin-stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.admin-stat-label {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.admin-members-section {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.admin-members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.admin-members-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.admin-search {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  width: 250px;
}

.admin-search:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-members-list {
  max-height: 400px;
  overflow-y: auto;
}

.admin-member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.admin-member-item:hover {
  background-color: #f8fafc;
}

.admin-member-item:last-child {
  border-bottom: none;
}

.admin-member-info {
  flex: 1;
}

.admin-member-name {
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.admin-member-details {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.admin-member-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.admin-member-status.active {
  background-color: #dcfce7;
  color: #166534;
}

.admin-member-status.inactive {
  background-color: #fef2f2;
  color: #dc2626;
}

.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: #64748b;
}

.admin-empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }
  
  .admin-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .admin-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .admin-members-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .admin-search {
    width: 100%;
  }
  
  .admin-member-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}



.invite-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.invite-form {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  align-items: flex-start;
}

.invite-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.invite-status {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
}

.invite-status.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.invite-status.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
  .invite-form {
    flex-direction: column;
  }
  
  .invite-input {
    width: 100%;
  }
}