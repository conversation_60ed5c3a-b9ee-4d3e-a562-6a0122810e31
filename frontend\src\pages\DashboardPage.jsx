// frontend/src/pages/DashboardPage.jsx

import React, { useState, useEffect, useCallback } from "react"; // <-- IMPORT useCallback
import { Link } from "react-router-dom";
import { getDashboardData } from "../services/api";
import { useAuth } from "../context/AuthContext";
import PaperCard from "../components/PaperCard"; // Assuming PaperCard is used here now

const DashboardPage = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getDashboardData();
        setDashboardData(response.data);
      } catch (err) {
        console.error("Dashboard fetch error:", err);
        setError("Could not load your dashboard.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []); // This useEffect only runs once, which is correct.

  // --- THIS IS THE FIX ---
  // We wrap the setPapers function in useCallback.
  // This tells React to "memoize" the function. It will only be recreated
  // if its own dependencies change (in this case, none).
  const handleSetPapers = useCallback((newPapers) => {
    setDashboardData((prevData) => {
      // It's safer to check if prevData exists
      if (!prevData) return null;
      return { ...prevData, papers: newPapers };
    });
  }, []); // The empty dependency array means this function is created only ONCE.

  if (loading) {
    return <div className="container">Loading Dashboard...</div>;
  }

  if (error) {
    return (
      <div className="container" style={{ color: "red" }}>
        {error}
      </div>
    );
  }

  // This is a placeholder for your real dashboard layout
  // We will use the paper list here as an example
  return (
    <div className="container">
      <h1>Welcome, {user?.first_name || user?.username}!</h1>
      <h2>Your Research Feed</h2>
      <div className="papers-list" style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        {dashboardData &&
          dashboardData.papers &&
          dashboardData.papers.map((paper) => (
            <PaperCard
              key={paper.id}
              paper={paper}
              papers={dashboardData.papers}
              // Now we pass the STABLE function as the prop
              setPapers={handleSetPapers}
            />
          ))}
      </div>
    </div>
  );
};

export default DashboardPage;



// // frontend/src/pages/DashboardPage.jsx
// import React, { useState, useEffect, useCallback } from "react";
// import { Link } from "react-router-dom";
// import { getDashboardData, getRecentActivity, getUpcomingDeadlines } from "../services/api";
// import { useAuth } from "../context/AuthContext";
// import {
//   Calendar,
//   FileText,
//   TrendingUp,
//   Users,
//   AlertCircle,
//   Clock,
//   Loader2,
//   RefreshCw,
//   ArrowRight,
//   BookOpen,
//   BarChart3,
// } from "lucide-react";

// const DashboardPage = () => {
//   const { user, logout } = useAuth();
//   const [dashboardData, setDashboardData] = useState(null);
//   const [recentActivity, setRecentActivity] = useState([]);
//   const [upcomingDeadlines, setUpcomingDeadlines] = useState([]);
//   const [loading, setLoading] = useState(true);
//   const [refreshing, setRefreshing] = useState(false);
//   const [error, setError] = useState("");

//   // Use useCallback to memoize the function and prevent infinite re-renders
//   const fetchDashboardData = useCallback(async (isRefresh = false) => {
//     try {
//       if (isRefresh) setRefreshing(true);
//       else setLoading(true);

//       setError("");

//       const [dashboardRes, activityRes, deadlinesRes] = await Promise.allSettled([
//         getDashboardData(),
//         getRecentActivity(),
//         getUpcomingDeadlines(),
//       ]);

//       if (dashboardRes.status === "fulfilled") {
//         setDashboardData(dashboardRes.value.data);
//       }

//       if (activityRes.status === "fulfilled") {
//         setRecentActivity(activityRes.value.data);
//       }

//       if (deadlinesRes.status === "fulfilled") {
//         setUpcomingDeadlines(deadlinesRes.value.data);
//       }

//       // Handle individual errors
//       const errors = [
//         dashboardRes.status === "rejected" && "dashboard data",
//         activityRes.status === "rejected" && "recent activity",
//         deadlinesRes.status === "rejected" && "upcoming deadlines",
//       ].filter(Boolean);

//       if (errors.length > 0) {
//         setError(`Failed to load ${errors.join(", ")}. Some data may be incomplete.`);
//       }
//     } catch (err) {
//       console.error("Dashboard fetch error:", err);
//       setError("Could not load your dashboard. Please try again later.");
//     } finally {
//       setLoading(false);
//       setRefreshing(false);
//     }
//   }, []); // Empty dependency array since we don't use any external variables

//   useEffect(() => {
//     fetchDashboardData();
//   }, [fetchDashboardData]); // Add fetchDashboardData to dependencies

//   const handleRefresh = () => {
//     fetchDashboardData(true);
//   };

//   // Rest of your component remains the same...
//   const StatsCard = ({ title, value, icon: Icon, trend, subtitle, color = "blue" }) => (
//     <div style={{ ...styles.statsCard, borderLeft: `4px solid var(--color-${color})` }}>
//       <div style={styles.statsIcon}>
//         <Icon size={20} />
//       </div>
//       <div style={styles.statsContent}>
//         <h3 style={styles.statsTitle}>{title}</h3>
//         <p style={styles.statsValue}>{value}</p>
//         {subtitle && <p style={styles.statsSubtitle}>{subtitle}</p>}
//       </div>
//       {trend && (
//         <div
//           style={{
//             ...styles.trend,
//             color: trend > 0 ? "#10b981" : "#ef4444",
//           }}
//         >
//           {trend > 0 ? "↑" : "↓"} {Math.abs(trend)}%
//         </div>
//       )}
//     </div>
//   );

//   const QuickAction = ({ icon: Icon, title, description, to, action }) => (
//     <div style={styles.quickActionCard}>
//       <div style={styles.quickActionIcon}>
//         <Icon size={24} />
//       </div>
//       <div style={styles.quickActionContent}>
//         <h4 style={styles.quickActionTitle}>{title}</h4>
//         <p style={styles.quickActionDescription}>{description}</p>
//       </div>
//       {to ? (
//         <Link to={to} style={styles.quickActionLink}>
//           <ArrowRight size={16} />
//         </Link>
//       ) : (
//         <button onClick={action} style={styles.quickActionButton}>
//           <ArrowRight size={16} />
//         </button>
//       )}
//     </div>
//   );

//   if (loading) {
//     return (
//       <div style={styles.loadingContainer}>
//         <Loader2 size={32} style={styles.spinner} />
//         <p>Loading your dashboard...</p>
//       </div>
//     );
//   }

//   return (
//     <div style={styles.container}>
//       {/* Header */}
//       <header style={styles.header}>
//         <div style={styles.headerContent}>
//           <div>
//             <h1 style={styles.welcomeTitle}>Welcome back, {user?.first_name || user?.username}!</h1>
//             <p style={styles.welcomeSubtitle}>Here's what's happening in your research world today.</p>
//           </div>
//           <div style={styles.headerActions}>
//             {/* Temporary admin dashboard link for testing */}
//             {(user?.can_supervise || user?.primary_role === 'Professor') && (
//               <Link to="/admin/dashboard" style={styles.adminLink}>
//                 Admin Dashboard
//               </Link>
//             )}
//             <button onClick={handleRefresh} disabled={refreshing} style={styles.refreshButton}>
//               <RefreshCw size={16} style={refreshing ? styles.spinning : {}} />
//               Refresh
//             </button>
//             <button onClick={logout} style={styles.logoutButton}>
//               Logout
//             </button>
//           </div>
//         </div>
//       </header>

//       {error && (
//         <div style={styles.errorBanner}>
//           <AlertCircle size={18} />
//           <span>{error}</span>
//         </div>
//       )}

//       {/* Stats Overview */}
//       <section style={styles.statsGrid}>
//         <StatsCard
//           icon={BookOpen}
//           title="Active Projects"
//           value={dashboardData?.stats?.active_projects || 0}
//           trend={12}
//           color="blue"
//         />
//         <StatsCard
//           icon={FileText}
//           title="Publications"
//           value={dashboardData?.stats?.publications || 0}
//           trend={8}
//           color="green"
//         />
//         <StatsCard
//           icon={Users}
//           title="Collaborators"
//           value={dashboardData?.stats?.collaborators || 0}
//           trend={5}
//           color="purple"
//         />
//         <StatsCard
//           icon={BarChart3}
//           title="Research Impact"
//           value={dashboardData?.stats?.citations || 0}
//           subtitle="Total citations"
//           color="orange"
//         />
//       </section>

//       {/* Main Content Grid */}
//       <div style={styles.mainGrid}>
//         {/* Left Column - Projects & Papers */}
//         <div style={styles.mainColumn}>
//           {/* Active Projects */}
//           <section style={styles.section}>
//             <div style={styles.sectionHeader}>
//               <h2 style={styles.sectionTitle}>
//                 <TrendingUp size={20} />
//                 Active Projects
//               </h2>
//               <Link to="/projects" style={styles.viewAllLink}>
//                 View all <ArrowRight size={14} />
//               </Link>
//             </div>
//             <div style={styles.cardsGrid}>
//               {dashboardData?.active_projects?.length > 0 ? (
//                 dashboardData.active_projects.slice(0, 3).map((project) => (
//                   <div key={project.id} style={styles.card}>
//                     <h3 style={styles.cardTitle}>{project.title}</h3>
//                     <div style={styles.cardMeta}>
//                       <span
//                         style={{
//                           ...styles.statusBadge,
//                           ...styles[`status-${project.status.toLowerCase()}`],
//                         }}
//                       >
//                         {project.status}
//                       </span>
//                       <span style={styles.metaText}>{project.completion}% complete</span>
//                     </div>
//                     <p style={styles.cardDescription}>{project.description}</p>
//                     {project.deadline && (
//                       <div style={styles.deadline}>
//                         <Clock size={14} />
//                         Due {new Date(project.deadline).toLocaleDateString()}
//                       </div>
//                     )}
//                   </div>
//                 ))
//               ) : (
//                 <div style={styles.emptyState}>
//                   <FileText size={32} />
//                   <p>No active projects</p>
//                   <Link to="/projects/new" style={styles.ctaLink}>
//                     Start your first project
//                   </Link>
//                 </div>
//               )}
//             </div>
//           </section>

//           {/* Recent Papers */}
//           <section style={styles.section}>
//             <div style={styles.sectionHeader}>
//               <h2 style={styles.sectionTitle}>
//                 <FileText size={20} />
//                 Recent Publications
//               </h2>
//               <Link to="/publications" style={styles.viewAllLink}>
//                 View all <ArrowRight size={14} />
//               </Link>
//             </div>
//             {dashboardData?.recent_papers?.length > 0 ? (
//               dashboardData.recent_papers.slice(0, 5).map((paper) => (
//                 <div key={paper.id} className="dashboard-list-item" style={styles.listItem}>
//                   <div style={styles.listItemContent}>
//                     <h4 style={styles.listItemTitle}>{paper.title}</h4>
//                     <p style={styles.listItemMeta}>
//                       {paper.source_journal} • {new Date(paper.publication_date).getFullYear()}
//                     </p>
//                   </div>
//                   <div style={styles.listItemBadge}>{paper.citations} citations</div>
//                 </div>
//               ))
//             ) : (
//               <div style={styles.emptyState}>
//                 <BookOpen size={32} />
//                 <p>No publications yet</p>
//               </div>
//             )}
//           </section>
//         </div>

//         {/* Right Column - Quick Actions & Activity */}
//         <div style={styles.sidebar}>
//           {/* Quick Actions */}
//           <section style={styles.section}>
//             <h2 style={styles.sectionTitle}>Quick Actions</h2>
//             <div style={styles.quickActions}>
//               <QuickAction
//                 icon={FileText}
//                 title="New Project"
//                 description="Start a new research project"
//                 to="/projects/new"
//               />
//               <QuickAction
//                 icon={BookOpen}
//                 title="Add Publication"
//                 description="Record a new publication"
//                 to="/publications/new"
//               />
//               <QuickAction
//                 icon={Users}
//                 title="Invite Collaborator"
//                 description="Add team members to your projects"
//                 to="/collaborators/invite"
//               />
//             </div>
//           </section>

//           {/* Upcoming Deadlines */}
//           <section style={styles.section}>
//             <h2 style={styles.sectionTitle}>Upcoming Deadlines</h2>
//             {upcomingDeadlines.length > 0 ? (
//               upcomingDeadlines.slice(0, 3).map((deadline) => (
//                 <div key={deadline.id} className="dashboard-deadline-item" style={styles.deadlineItem}>
//                   <div style={styles.deadlineDate}>
//                     {new Date(deadline.date).toLocaleDateString("en-US", {
//                       month: "short",
//                       day: "numeric",
//                     })}
//                   </div>
//                   <div style={styles.deadlineContent}>
//                     <h4 style={styles.deadlineTitle}>{deadline.title}</h4>
//                     <p style={styles.deadlineProject}>{deadline.project}</p>
//                   </div>
//                 </div>
//               ))
//             ) : (
//               <p style={styles.emptyText}>No upcoming deadlines</p>
//             )}
//           </section>

//           {/* Recent Activity */}
//           <section style={styles.section}>
//             <h2 style={styles.sectionTitle}>Recent Activity</h2>
//             {recentActivity.length > 0 ? (
//               recentActivity.slice(0, 5).map((activity) => (
//                 <div key={activity.id} className="dashboard-activity-item" style={styles.activityItem}>
//                   <div style={styles.activityIcon}>
//                     {activity.type === "publication" && <FileText size={14} />}
//                     {activity.type === "project" && <TrendingUp size={14} />}
//                     {activity.type === "collaboration" && <Users size={14} />}
//                   </div>
//                   <div style={styles.activityContent}>
//                     <p style={styles.activityText}>{activity.description}</p>
//                     <span style={styles.activityTime}>{activity.time}</span>
//                   </div>
//                 </div>
//               ))
//             ) : (
//               <p style={styles.emptyText}>No recent activity</p>
//             )}
//           </section>
//         </div>
//       </div>
//     </div>
//   );
// };

// // Your styles object remains exactly the same...
// const styles = {
//   container: {
//     maxWidth: "1400px",
//     margin: "0 auto",
//     padding: "24px",
//     minHeight: "100vh",
//     backgroundColor: "#f8fafc",
//   },
//   loadingContainer: {
//     display: "flex",
//     flexDirection: "column",
//     alignItems: "center",
//     justifyContent: "center",
//     minHeight: "400px",
//     color: "#64748b",
//   },
//   header: {
//     marginBottom: "32px",
//   },
//   headerContent: {
//     display: "flex",
//     justifyContent: "space-between",
//     alignItems: "flex-start",
//     gap: "20px",
//   },
//   welcomeTitle: {
//     fontSize: "2rem",
//     fontWeight: "700",
//     color: "#1e293b",
//     margin: "0 0 8px 0",
//   },
//   welcomeSubtitle: {
//     fontSize: "1.1rem",
//     color: "#64748b",
//     margin: "0",
//   },
//   headerActions: {
//     display: "flex",
//     gap: "12px",
//   },
//   refreshButton: {
//     display: "flex",
//     alignItems: "center",
//     gap: "8px",
//     padding: "8px 16px",
//     border: "1px solid #e2e8f0",
//     borderRadius: "6px",
//     background: "white",
//     color: "#64748b",
//     cursor: "pointer",
//     fontSize: "0.875rem",
//     transition: "all 0.2s ease",
//     ":hover": {
//       borderColor: "#cbd5e1",
//       color: "#374151",
//     },
//     ":disabled": {
//       opacity: "0.6",
//       cursor: "not-allowed",
//     },
//   },
//   logoutButton: {
//     padding: "8px 16px",
//     border: "1px solid #dc2626",
//     borderRadius: "6px",
//     background: "#dc2626",
//     color: "white",
//     cursor: "pointer",
//     fontSize: "0.875rem",
//     transition: "all 0.2s ease",
//     ":hover": {
//       backgroundColor: "#b91c1c",
//       borderColor: "#b91c1c",
//     },
//   },
//   adminLink: {
//     display: "flex",
//     alignItems: "center",
//     padding: "8px 16px",
//     border: "1px solid #3b82f6",
//     borderRadius: "6px",
//     background: "#3b82f6",
//     color: "white",
//     textDecoration: "none",
//     fontSize: "0.875rem",
//     transition: "all 0.2s ease",
//     ":hover": {
//       backgroundColor: "#2563eb",
//       borderColor: "#2563eb",
//     },
//   },
//   errorBanner: {
//     display: "flex",
//     alignItems: "center",
//     gap: "8px",
//     padding: "12px 16px",
//     backgroundColor: "#fef2f2",
//     border: "1px solid #fecaca",
//     borderRadius: "6px",
//     color: "#dc2626",
//     marginBottom: "24px",
//     fontSize: "0.875rem",
//   },
//   statsGrid: {
//     display: "grid",
//     gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
//     gap: "20px",
//     marginBottom: "32px",
//   },
//   statsCard: {
//     display: "flex",
//     alignItems: "center",
//     gap: "16px",
//     padding: "24px",
//     backgroundColor: "white",
//     borderRadius: "12px",
//     boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
//     borderLeft: "4px solid #3b82f6",
//   },
//   statsIcon: {
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     width: "48px",
//     height: "48px",
//     borderRadius: "8px",
//     backgroundColor: "#eff6ff",
//     color: "#3b82f6",
//   },
//   statsContent: {
//     flex: 1,
//   },
//   statsTitle: {
//     fontSize: "0.875rem",
//     fontWeight: "500",
//     color: "#64748b",
//     margin: "0 0 4px 0",
//   },
//   statsValue: {
//     fontSize: "1.5rem",
//     fontWeight: "700",
//     color: "#1e293b",
//     margin: "0",
//   },
//   statsSubtitle: {
//     fontSize: "0.75rem",
//     color: "#64748b",
//     margin: "4px 0 0 0",
//   },
//   trend: {
//     fontSize: "0.875rem",
//     fontWeight: "600",
//   },
//   mainGrid: {
//     display: "grid",
//     gridTemplateColumns: "2fr 1fr",
//     gap: "32px",
//     alignItems: "start",
//   },
//   mainColumn: {
//     display: "flex",
//     flexDirection: "column",
//     gap: "32px",
//   },
//   sidebar: {
//     display: "flex",
//     flexDirection: "column",
//     gap: "32px",
//   },
//   section: {
//     backgroundColor: "white",
//     borderRadius: "12px",
//     padding: "24px",
//     boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
//   },
//   sectionHeader: {
//     display: "flex",
//     justifyContent: "space-between",
//     alignItems: "center",
//     marginBottom: "20px",
//   },
//   sectionTitle: {
//     display: "flex",
//     alignItems: "center",
//     gap: "8px",
//     fontSize: "1.25rem",
//     fontWeight: "600",
//     color: "#1e293b",
//     margin: "0",
//   },
//   viewAllLink: {
//     display: "flex",
//     alignItems: "center",
//     gap: "4px",
//     fontSize: "0.875rem",
//     color: "#3b82f6",
//     textDecoration: "none",
//     fontWeight: "500",
//     ":hover": {
//       textDecoration: "underline",
//     },
//   },
//   cardsGrid: {
//     display: "flex",
//     flexDirection: "column",
//     gap: "16px",
//   },
//   card: {
//     padding: "20px",
//     border: "1px solid #e2e8f0",
//     borderRadius: "8px",
//     transition: "all 0.2s ease",
//     ":hover": {
//       borderColor: "#cbd5e1",
//       boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
//     },
//   },
//   cardTitle: {
//     fontSize: "1rem",
//     fontWeight: "600",
//     color: "#1e293b",
//     margin: "0 0 12px 0",
//   },
//   cardMeta: {
//     display: "flex",
//     alignItems: "center",
//     gap: "12px",
//     marginBottom: "12px",
//   },
//   statusBadge: {
//     padding: "4px 8px",
//     borderRadius: "4px",
//     fontSize: "0.75rem",
//     fontWeight: "500",
//   },
//   "status-active": {
//     backgroundColor: "#dcfce7",
//     color: "#166534",
//   },
//   "status-pending": {
//     backgroundColor: "#fef3c7",
//     color: "#92400e",
//   },
//   "status-completed": {
//     backgroundColor: "#dbeafe",
//     color: "#1e40af",
//   },
//   metaText: {
//     fontSize: "0.875rem",
//     color: "#64748b",
//   },
//   cardDescription: {
//     fontSize: "0.875rem",
//     color: "#64748b",
//     margin: "0 0 12px 0",
//     lineHeight: "1.5",
//   },
//   deadline: {
//     display: "flex",
//     alignItems: "center",
//     gap: "6px",
//     fontSize: "0.875rem",
//     color: "#ef4444",
//     fontWeight: "500",
//   },
//   listItem: {
//     // Note: Using CSS class 'dashboard-list-item' for :last-child selector
//   },
//   listItemContent: {
//     flex: 1,
//   },
//   listItemTitle: {
//     fontSize: "0.875rem",
//     fontWeight: "500",
//     color: "#1e293b",
//     margin: "0 0 4px 0",
//     lineHeight: "1.4",
//   },
//   listItemMeta: {
//     fontSize: "0.75rem",
//     color: "#64748b",
//     margin: "0",
//   },
//   listItemBadge: {
//     fontSize: "0.75rem",
//     padding: "4px 8px",
//     backgroundColor: "#f1f5f9",
//     borderRadius: "12px",
//     color: "#64748b",
//     fontWeight: "500",
//   },
//   emptyState: {
//     display: "flex",
//     flexDirection: "column",
//     alignItems: "center",
//     justifyContent: "center",
//     padding: "40px 20px",
//     color: "#64748b",
//     textAlign: "center",
//   },
//   emptyText: {
//     color: "#64748b",
//     fontSize: "0.875rem",
//     margin: "0",
//     textAlign: "center",
//   },
//   ctaLink: {
//     color: "#3b82f6",
//     textDecoration: "none",
//     fontWeight: "500",
//     fontSize: "0.875rem",
//     marginTop: "8px",
//     ":hover": {
//       textDecoration: "underline",
//     },
//   },
//   quickActions: {
//     display: "flex",
//     flexDirection: "column",
//     gap: "12px",
//   },
//   quickActionCard: {
//     display: "flex",
//     alignItems: "center",
//     gap: "12px",
//     padding: "16px",
//     border: "1px solid #e2e8f0",
//     borderRadius: "8px",
//     transition: "all 0.2s ease",
//     ":hover": {
//       borderColor: "#cbd5e1",
//       backgroundColor: "#f8fafc",
//     },
//   },
//   quickActionIcon: {
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     width: "40px",
//     height: "40px",
//     borderRadius: "6px",
//     backgroundColor: "#eff6ff",
//     color: "#3b82f6",
//     flexShrink: 0,
//   },
//   quickActionContent: {
//     flex: 1,
//   },
//   quickActionTitle: {
//     fontSize: "0.875rem",
//     fontWeight: "600",
//     color: "#1e293b",
//     margin: "0 0 4px 0",
//   },
//   quickActionDescription: {
//     fontSize: "0.75rem",
//     color: "#64748b",
//     margin: "0",
//   },
//   quickActionLink: {
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     width: "32px",
//     height: "32px",
//     borderRadius: "6px",
//     color: "#3b82f6",
//     textDecoration: "none",
//     transition: "all 0.2s ease",
//     ":hover": {
//       backgroundColor: "#eff6ff",
//     },
//   },
//   quickActionButton: {
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     width: "32px",
//     height: "32px",
//     borderRadius: "6px",
//     border: "none",
//     background: "none",
//     color: "#3b82f6",
//     cursor: "pointer",
//     transition: "all 0.2s ease",
//     ":hover": {
//       backgroundColor: "#eff6ff",
//     },
//   },
//   deadlineItem: {
//     // Note: Using CSS class 'dashboard-deadline-item' for :last-child selector
//   },
//   deadlineDate: {
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     width: "40px",
//     height: "40px",
//     borderRadius: "6px",
//     backgroundColor: "#fef3c7",
//     color: "#92400e",
//     fontWeight: "600",
//     fontSize: "0.75rem",
//     flexShrink: 0,
//   },
//   deadlineContent: {
//     flex: 1,
//   },
//   deadlineTitle: {
//     fontSize: "0.875rem",
//     fontWeight: "500",
//     color: "#1e293b",
//     margin: "0 0 2px 0",
//   },
//   deadlineProject: {
//     fontSize: "0.75rem",
//     color: "#64748b",
//     margin: "0",
//   },
//   activityItem: {
//     // Note: Using CSS class 'dashboard-activity-item' for :last-child selector
//   },
//   activityIcon: {
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     width: "24px",
//     height: "24px",
//     borderRadius: "4px",
//     backgroundColor: "#f1f5f9",
//     color: "#64748b",
//     flexShrink: 0,
//     marginTop: "2px",
//   },
//   activityContent: {
//     flex: 1,
//   },
//   activityText: {
//     fontSize: "0.875rem",
//     color: "#1e293b",
//     margin: "0 0 4px 0",
//     lineHeight: "1.4",
//   },
//   activityTime: {
//     fontSize: "0.75rem",
//     color: "#64748b",
//   },
//   spinning: {
//     animation: "spin 1s linear infinite",
//   },
// };

// export default DashboardPage;
