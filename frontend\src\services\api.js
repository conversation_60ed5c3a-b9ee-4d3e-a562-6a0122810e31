// frontend/src/services/api.js

import axios from "axios";

// Create a pre-configured instance of axios.
// All requests made with this instance will go to your Django backend.
export const apiClient = axios.create({
  baseURL: "http://127.0.0.1:8000/api/", // The base URL of your Django API (change this to your actual backend URL)
});

/**
 * A specific function for logging in.
 * It sends the username and password to the JWT token endpoint.
 * @param {string} username - The user's username.
 * @param {string} password - The user's password.
 * @returns {Promise} The axios promise.
 */
export const loginUser = (username, password) => {
  return apiClient.post("auth/token/", { username, password });
};

/**
 * Fetches the aggregated data for the main dashboard.
 */
export const getDashboardData = () => {
  return apiClient.get("research/dashboard/");
};

export const getRecentActivity = async () => {
  try {
    const response = await apiClient.get("research/activity/recent/");
    return response;
  } catch (error) {
    console.error("Error fetching recent activity:", error);
    throw error;
  }
};

export const getUpcomingDeadlines = async () => {
  try {
    const response = await apiClient.get("research/deadlines/upcoming/");
    return response;
  } catch (error) {
    console.error("Error fetching upcoming deadlines:", error);
    throw error;
  }
};

/**
 * Fetches admin statistics (mock data for now)
 */
export const getAdminStats = async () => {
  try {
    // For now, return mock data. In a real app, this would call your admin API
    return {
      data: {
        totalUsers: 156,
        totalPapers: 2847,
        totalProjects: 89,
        activeUsers: 42,
        newUsersThisMonth: 23,
        papersThisMonth: 145,
      },
    };
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    throw error;
  }
};

/**
 * Fetches admin members list (mock data for now)
 */
export const getAdminMembers = async () => {
  try {
    // For now, return mock data. In a real app, this would call your admin API
    return {
      data: [
        {
          id: 1,
          name: "Dr. Sarah Johnson",
          email: "<EMAIL>",
          role: "Professor",
          department: "Computer Science",
          joinDate: "2023-01-15",
          status: "active",
        },
        {
          id: 2,
          name: "Dr. Michael Chen",
          email: "<EMAIL>",
          role: "Associate Professor",
          department: "Data Science",
          joinDate: "2023-03-20",
          status: "active",
        },
        {
          id: 3,
          name: "Dr. Emily Rodriguez",
          email: "<EMAIL>",
          role: "Assistant Professor",
          department: "Machine Learning",
          joinDate: "2023-06-10",
          status: "active",
        },
      ],
    };
  } catch (error) {
    console.error("Error fetching admin members:", error);
    throw error;
  }
};

/**
 * Fetches admin workspace/research group information
 */
export const getAdminWorkspace = async () => {
  try {
    // For now, return mock data. In a real app, this would call your admin API
    return {
      data: {
        id: 1,
        research_group_id: 1,
        name: "Research Group 1",
      },
    };
  } catch (error) {
    console.error("Error fetching admin workspace:", error);
    throw error;
  }
};

/**
 * Fetches the details for a single project by its ID.
 * @param {string} projectId The UUID of the project.
 */
export const getProjectDetail = (projectId) => {
  return apiClient.get(`research/projects/${projectId}/`);
};

/**
 * Sends a new message to a project's chat.
 * @param {string} projectId The UUID of the project.
 * @param {string} content The text content of the message.
 */
export const sendProjectMessage = (projectId, content) => {
  return apiClient.post(`research/projects/${projectId}/messages/`, { content });
};

/**
 * Fetches messages for a project.
 * @param {string} projectId The UUID of the project.
 */
export const getProjectMessages = (projectId) => {
  return apiClient.get(`research/projects/${projectId}/messages/`);
};

/**
 * Updates project content.
 * @param {string} projectId The UUID of the project.
 * @param {string} content The updated content.
 */
export const updateProjectContent = (projectId, content) => {
  return apiClient.patch(`research/projects/${projectId}/`, { content });
};

/**
 * Clears project cache (mock function for now).
 * @param {string} projectId The UUID of the project.
 */
export const clearProjectCache = (projectId) => {
  // Mock function - in a real app this might clear cache on the server
  return Promise.resolve({ success: true });
};

/**
 * Fetches all papers for the current user.
 */
export const getPapers = async () => {
  try {
    const response = await apiClient.get("research/papers/");
    return response;
  } catch (error) {
    console.error("Error fetching papers:", error);
    throw error;
  }
};

/**
 * Creates a new paper.
 * @param {Object} paperData The paper data to create.
 */
export const createPaper = async (paperData) => {
  try {
    const response = await apiClient.post("research/papers/", paperData);
    return response;
  } catch (error) {
    console.error("Error creating paper:", error);
    throw error;
  }
};

/**
 * Deletes a paper by ID.
 * @param {string} paperId The UUID of the paper to delete.
 */
export const deletePaper = async (paperId) => {
  try {
    const response = await apiClient.delete(`research/papers/${paperId}/`);
    return response;
  } catch (error) {
    console.error("Error deleting paper:", error);
    throw error;
  }
};

/**
 * Fetches all projects for the current user.
 */
export const getProjects = async () => {
  try {
    const response = await apiClient.get("research/projects/");
    return response;
  } catch (error) {
    console.error("Error fetching projects:", error);
    throw error;
  }
};

/**
 * Creates a new project.
 * @param {Object} projectData The project data to create.
 */
export const createProject = async (projectData) => {
  try {
    const response = await apiClient.post("research/projects/", projectData);
    return response;
  } catch (error) {
    console.error("Error creating project:", error);
    throw error;
  }
};

/**
 * Sends an invitation from a research group to a new user's email.
 * @param {string} groupId - The UUID of the research group sending the invite.
 * @param {string} email - The email address of the person being invited.
 * @param {string} roleId - The UUID of the RoleType they should be assigned.
 */
export const createGroupInvitation = (groupId, email, roleId) => {
  // This calls the InvitationViewSet create action
  return apiClient.post(`accounts/invitations/`, {
    email: email,
    intended_role: roleId,
    research_group: groupId
  });
};

/**
 * Fetches the details of a specific invitation using its code.
 * This is used by the registration page to verify the code and get the invitee's email.
 * This endpoint is publicly accessible (no authentication required).
 * @param {string} invitationCode - The UUID code from the invitation link.
 */
export const getInvitationDetails = (invitationCode) => {
  return apiClient.get(`accounts/invitations/validate/${invitationCode}/`);
};

/**
 * Registers a new student using a validated invitation.
 * @param {object} userData - The user's registration data (username, password, code, etc.)
 */
export const registerStudent = (userData) => {
  // This calls our existing registration endpoint, which is now smarter
  return apiClient.post("auth/register/", userData);
};

/**
 * Registers a new institution
 */
export const registerInstitution = (formData) => {
  // The correct URL is now /api/auth/institution/register/
  return apiClient.post("auth/institution/register/", formData);
};

/**
 * Fetches the research groups the current user is a member of
 */
export const getMyResearchGroups = () => {
  console.log("🔍 DEBUG: API Client headers:", apiClient.defaults.headers.common);
  console.log("🔍 DEBUG: Making request to accounts/research-groups/");
  return apiClient.get("accounts/research-groups/");
};

/**
 * Fetches the list of members for the admin's research groups
 */
export const getGroupMembers = () => {
  return apiClient.get(`accounts/admin/members/`);
};

/**
 * Fetches student dashboard data
 */
export const getStudentDashboardData = () => {
  return apiClient.get("research/dashboard/"); // The endpoint is in research/urls.py
};

/**
 * Registers a new user
 */
export const registerUser = (userData) => {
  return apiClient.post("accounts/register/", userData);
};

/**
 * Fetches all available role types from the backend.
 */
export const getRoleTypes = () => {
  return apiClient.get("accounts/roles/");
};

/**
 * Fetches existing invitations for the current user's research groups.
 */
export const getMyInvitations = () => {
  return apiClient.get('accounts/invitations/');
};


// Professor Dashboard APIs
export const getMyStudents = () => {
  return apiClient.get('accounts/professor/students/');
};

export const getSupervisionRequests = () => {
  return apiClient.get('accounts/professor/supervision-requests/');
};

export const startSupervision = (studentId) => {
  return apiClient.post(`accounts/professor/students/${studentId}/start-supervision/`);
};

export const approveSupervisionRequest = (requestId) => {
  return apiClient.post(`accounts/professor/supervision-requests/${requestId}/approve/`);
};

export const rejectSupervisionRequest = (requestId) => {
  return apiClient.post(`accounts/professor/supervision-requests/${requestId}/reject/`);
};


// --- ADD THIS NEW FUNCTION ---
/**
 * Debug endpoint to check authentication status.
 */
export const debugAuth = () => {
    return apiClient.get('accounts/admin/debug-auth/');
};

/**
 * Triggers the backend to create a new supervision project.
 * @param {string} studentId The UUID of the student to supervise.
 */
export const startSupervisionProject = (studentId) => {
    return apiClient.post('accounts/admin/start-supervision/', { student_id: studentId });
};

/**
 * Fetches the list of projects the current user is a participant in.
 */
export const getMyProjects = () => {
    // --- THIS IS THE FIX ---
    // The correct URL is /api/research/projects/
    return apiClient.get('research/projects/');
};

/**
 * Creates a new project.
 * @param {object} projectData - The data for the new project.
 */
export const createNewProject = (projectData) => {
    // --- THIS IS THE FIX ---
    // The correct URL is /api/research/projects/
    return apiClient.post('research/projects/', projectData);
};


