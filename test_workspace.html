<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Workspace Tabs</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            margin: 20px;
            background: #f8fafc;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 20px 0;
        }
        
        .header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .tabs {
            display: flex;
            gap: 8px;
        }
        
        .tab {
            display: flex;
            alignItems: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }
        
        .tab:hover {
            background: #f1f5f9;
            color: #475569;
        }
        
        .tab.active {
            background: #14b8a6;
            color: white;
            border-color: #14b8a6;
        }
        
        .workspace {
            min-height: 200px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <h1>Workspace Tab Test</h1>
    
    <div class="container">
        <div class="header">
            <h2>Research Workspace</h2>
            <div class="tabs">
                <button class="tab active" onclick="setActive(this)">
                    📝 Writing
                </button>
                <button class="tab" onclick="setActive(this)">
                    💬 Chat
                </button>
                <button class="tab" onclick="setActive(this)">
                    📄 Manuscripts
                </button>
            </div>
        </div>
        
        <div class="workspace">
            <p>Tab content would appear here. The tabs should now be clearly visible with dark text.</p>
        </div>
    </div>

    <script>
        function setActive(clickedTab) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Add active class to clicked tab
            clickedTab.classList.add('active');
        }
    </script>
</body>
</html>
