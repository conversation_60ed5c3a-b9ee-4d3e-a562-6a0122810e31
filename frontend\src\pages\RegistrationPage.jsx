import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { getInvitationDetails, registerUser } from "../services/api";
import { Eye, EyeOff, UserPlus, Loader2, CheckCircle } from "lucide-react";

const RegistrationPage = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // 1. Get the INVITATION CODE from the URL query parameters.
  const queryParams = new URLSearchParams(location.search);
  const invitationCode = queryParams.get("code"); // The URL will be like /register?code=...

  // --- CONSOLIDATED STATE ---
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    first_name: "",
    last_name: "",
    email: "", // Added missing email field
    // This is what the backend serializer expects.
    code: invitationCode || "",
  });
  const [invitationDetails, setInvitationDetails] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false); // Added missing state
  const [pageState, setPageState] = useState("loading"); // 'loading', 'form', 'success', 'error'
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false); // Added missing state

  // 2. When the page loads, verify the invitation code with the backend.
  useEffect(() => {
    if (!invitationCode) {
      setErrorMessage("No invitation code provided. Please use the link from your invitation email.");
      setPageState("error");
      return;
    }

    const verifyCode = async () => {
      try {
        const response = await getInvitationDetails(invitationCode);
        setInvitationDetails(response.data);
        // Pre-fill and lock the email address for security.
        setFormData((prev) => ({
          ...prev,
          email: response.data.invitee_email,
        }));
        setPageState("form"); // Show the form after verification
      } catch (err) {
        setErrorMessage(err.response?.data?.detail || "This invitation is invalid or has already been used.");
        setPageState("error");
      }
    };
    verifyCode();
  }, [invitationCode]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      setErrorMessage("Passwords do not match.");
      return false;
    }
    if (formData.password.length < 8) {
      setErrorMessage("Password must be at least 8 characters long.");
      return false;
    }
    return true;
  };

  // 3. The submit handler uses the `registerUser` function and sends the full form data.
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsLoading(true);
    setErrorMessage("");

    try {
      // The formData already contains the invitation_code
      await registerUser(formData);
      setPageState("success");
      setTimeout(() => navigate("/login"), 3000);
    } catch (err) {
      const apiError =
        err.response?.data?.username?.[0] ||
        err.response?.data?.email?.[0] ||
        err.response?.data?.password?.[0] ||
        err.response?.data?.detail ||
        "Registration failed. Please check your details.";
      setErrorMessage(apiError);
      setPageState("form"); // Return to form to show the error
    } finally {
      setIsLoading(false);
    }
  };

  // --- RENDER LOGIC BASED ON PAGE STATE ---

  if (pageState === "loading") {
    return (
      <div style={styles.container}>
        <div style={styles.formContainer}>
          <Loader2 size={32} style={styles.spinner} />
          <p>Verifying invitation...</p>
        </div>
      </div>
    );
  }

  if (pageState === "success") {
    return (
      <div style={styles.container}>
        <div style={styles.successContainer}>
          <CheckCircle size={48} style={styles.successIcon} />
          <h2>Registration Successful!</h2>
          <p>You will be redirected to the login page shortly.</p>
        </div>
      </div>
    );
  }

  if (pageState === "error") {
    return (
      <div style={styles.container}>
        <div style={styles.formContainer}>
          <h2>Invalid Invitation</h2>
          <p style={styles.error}>{errorMessage}</p>
          <Link to="/login" style={styles.link}>
            Go to Login Page
          </Link>
        </div>
      </div>
    );
  }

  // Default state is to render the form
  return (
    <div style={styles.container}>
      <div style={styles.formContainer}>
        <div style={styles.header}>
          <h1 style={styles.title}>Welcome to AcademiaSpace</h1>
          <p style={styles.subtitle}>Create your student account</p>
        </div>

        {invitationDetails && (
          <div style={styles.invitationInfo}>
            <p>
              Accepting invitation from <strong>{invitationDetails.inviter.display_name}</strong> to join{" "}
              <strong>{invitationDetails.research_group.name}</strong>.
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} style={styles.form}>
          <div style={styles.nameRow}>
            <div style={styles.inputGroup}>
              <label htmlFor="first_name" style={styles.label}>
                First Name
              </label>
              <input
                id="first_name"
                name="first_name"
                type="text"
                value={formData.first_name}
                onChange={handleChange}
                placeholder="Enter your first name"
                required
                disabled={isLoading}
                style={styles.input}
              />
            </div>
            <div style={styles.inputGroup}>
              <label htmlFor="last_name" style={styles.label}>
                Last Name
              </label>
              <input
                id="last_name"
                name="last_name"
                type="text"
                value={formData.last_name}
                onChange={handleChange}
                placeholder="Enter your last name"
                required
                disabled={isLoading}
                style={styles.input}
              />
            </div>
          </div>

          <div style={styles.inputGroup}>
            <label htmlFor="username" style={styles.label}>
              Username
            </label>
            <input
              id="username"
              name="username"
              type="text"
              value={formData.username}
              onChange={handleChange}
              placeholder="Choose a username"
              required
              disabled={isLoading}
              style={styles.input}
              autoComplete="username"
            />
          </div>

          <div style={styles.inputGroup}>
            <label htmlFor="email" style={styles.label}>
              Email
            </label>
            <input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              required
              disabled={true} // Email is pre-filled and disabled
              style={{ ...styles.input, backgroundColor: "#f9fafb" }}
              autoComplete="email"
            />
          </div>

          <div style={styles.inputGroup}>
            <label htmlFor="password" style={styles.label}>
              Password
            </label>
            <div style={styles.passwordContainer}>
              <input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={handleChange}
                placeholder="Create a password (min. 8 characters)"
                required
                disabled={isLoading}
                style={{ ...styles.input, paddingRight: "45px" }}
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                style={styles.passwordToggle}
                disabled={isLoading}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          <div style={styles.inputGroup}>
            <label htmlFor="confirmPassword" style={styles.label}>
              Confirm Password
            </label>
            <div style={styles.passwordContainer}>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Confirm your password"
                required
                disabled={isLoading}
                style={{ ...styles.input, paddingRight: "45px" }}
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                style={styles.passwordToggle}
                disabled={isLoading}
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          {errorMessage && (
            <div style={styles.errorContainer}>
              <p style={styles.error}>{errorMessage}</p>
            </div>
          )}

          <button
            type="submit"
            style={{
              ...styles.button,
              ...(isLoading ? styles.buttonLoading : {}),
            }}
            disabled={isLoading}
          >
            {isLoading ? <Loader2 size={20} style={styles.spinner} /> : <UserPlus size={20} />}
            {isLoading ? "Creating Account..." : "Create Account"}
          </button>

          <div style={styles.footer}>
            <p style={styles.footerText}>
              Already have an account?{" "}
              <Link to="/login" style={styles.link}>
                Sign in here
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

const styles = {
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    minHeight: "100vh",
    backgroundColor: "#f8fafc",
    padding: "20px",
  },
  formContainer: {
    padding: "2.5rem",
    background: "white",
    borderRadius: "12px",
    boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
    width: "100%",
    maxWidth: "500px",
    border: "1px solid #e2e8f0",
    textAlign: "center",
  },
  header: {
    textAlign: "center",
    marginBottom: "2rem",
  },
  title: {
    fontSize: "1.875rem",
    fontWeight: "700",
    color: "#1e293b",
    margin: "0 0 0.5rem 0",
  },
  subtitle: {
    fontSize: "1rem",
    color: "#64748b",
    margin: "0",
  },
  invitationInfo: {
    backgroundColor: "#f0f9ff",
    padding: "1rem",
    borderRadius: "8px",
    marginBottom: "1.5rem",
    border: "1px solid #bae6fd",
  },
  form: {
    display: "flex",
    flexDirection: "column",
    gap: "1.25rem",
  },
  nameRow: {
    display: "grid",
    gridTemplateColumns: "1fr 1fr",
    gap: "1rem",
  },
  inputGroup: {
    display: "flex",
    flexDirection: "column",
    gap: "0.5rem",
  },
  label: {
    fontSize: "0.875rem",
    fontWeight: "500",
    color: "#374151",
    textAlign: "left",
  },
  input: {
    padding: "0.75rem 1rem",
    borderRadius: "8px",
    border: "1px solid #d1d5db",
    fontSize: "1rem",
    transition: "all 0.2s ease",
    outline: "none",
    width: "100%",
    boxSizing: "border-box",
  },
  passwordContainer: {
    position: "relative",
    display: "flex",
    alignItems: "center",
  },
  passwordToggle: {
    position: "absolute",
    right: "12px",
    background: "none",
    border: "none",
    color: "#64748b",
    cursor: "pointer",
    padding: "4px",
    borderRadius: "4px",
  },
  errorContainer: {
    backgroundColor: "#fef2f2",
    border: "1px solid #fecaca",
    borderRadius: "6px",
    padding: "0.75rem",
  },
  error: {
    color: "#dc2626",
    fontSize: "0.875rem",
    margin: "0",
    textAlign: "center",
  },
  button: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "0.5rem",
    padding: "0.75rem 1.5rem",
    background: "#2563eb",
    color: "white",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
    fontSize: "1rem",
    fontWeight: "600",
    transition: "all 0.2s ease",
  },
  buttonLoading: {
    opacity: "0.8",
  },
  footer: {
    textAlign: "center",
    paddingTop: "1rem",
    borderTop: "1px solid #e2e8f0",
  },
  footerText: {
    margin: "0",
    fontSize: "0.875rem",
    color: "#64748b",
  },
  link: {
    color: "#2563eb",
    textDecoration: "none",
    fontWeight: "500",
  },
  successContainer: {
    textAlign: "center",
    padding: "3rem",
    background: "white",
    borderRadius: "12px",
    boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
    maxWidth: "400px",
  },
  successIcon: {
    color: "#10b981",
    marginBottom: "1rem",
  },
  spinner: {
    animation: "spin 1s linear infinite",
    margin: "0 auto",
  },
};

export default RegistrationPage;




// // frontend/src/pages/RegistrationPage.jsx
// import React, { useState, useEffect } from "react";
// import { Link, useNavigate, useLocation } from "react-router-dom";
// import { useAuth } from "../context/AuthContext";
// import { getInvitationDetails, registerStudent } from "../services/api";
// import { Eye, EyeOff, UserPlus, Loader2, CheckCircle } from "lucide-react";

// const RegistrationPage = () => {
//   const navigate = useNavigate();
//   const location = useLocation();
//   const { register } = useAuth();

//   // 1. Get the invitation token from the URL query parameters
//   const queryParams = new URLSearchParams(location.search);
//   const invitationToken = queryParams.get("token");

//   const [formData, setFormData] = useState({
//     username: "",
//     email: "",
//     password: "",
//     confirmPassword: "",
//     first_name: "",
//     last_name: "",
//   });
//   const [invitationDetails, setInvitationDetails] = useState(null);
//   const [showPassword, setShowPassword] = useState(false);
//   const [showConfirmPassword, setShowConfirmPassword] = useState(false);
//   const [error, setError] = useState("");
//   const [isLoading, setIsLoading] = useState(false);
//   const [loading, setLoading] = useState(true);
//   const [success, setSuccess] = useState(false);

//   // 2. When the page loads, verify the invitation token with the backend
//   useEffect(() => {
//     if (!invitationToken) {
//       setError("No invitation token provided. Please use the link from your invitation email.");
//       setLoading(false);
//       return;
//     }

//     const verifyToken = async () => {
//       try {
//         const response = await getInvitationDetails(invitationToken);
//         setInvitationDetails(response.data);
//         // Pre-fill and lock the email address for security
//         setFormData((prev) => ({
//           ...prev,
//           email: response.data.invitee_email,
//           first_name: response.data.first_name || "",
//           last_name: response.data.last_name || "",
//         }));
//       } catch (err) {
//         setError(err.response?.data?.error || "This invitation token is invalid, expired, or has already been used.");
//       } finally {
//         setLoading(false);
//       }
//     };

//     verifyToken();
//   }, [invitationToken]);

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }));
//     if (error) setError("");
//   };

//   const validateForm = () => {
//     if (formData.password !== formData.confirmPassword) {
//       setError("Passwords do not match");
//       return false;
//     }
//     if (formData.password.length < 8) {
//       setError("Password must be at least 8 characters long");
//       return false;
//     }
//     if (!formData.first_name || !formData.last_name) {
//       setError("First name and last name are required");
//       return false;
//     }
//     return true;
//   };

//   // 3. The submit handler now uses the `registerStudent` function
//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setError("");

//     if (!validateForm()) return;

//     setIsLoading(true);

//     try {
//       const registrationData = {
//         ...formData,
//         token: invitationToken, // Include the token in the registration request
//       };

//       await registerStudent(registrationData);
//       setSuccess(true);

//       // Redirect to login page after 3 seconds
//       setTimeout(() => {
//         navigate("/login");
//       }, 3000);
//     } catch (err) {
//       // Display specific validation errors from the backend if they exist
//       const apiError =
//         err.response?.data?.username?.[0] ||
//         err.response?.data?.email?.[0] ||
//         err.response?.data?.password?.[0] ||
//         err.response?.data?.detail ||
//         "Registration failed. Please check your details.";
//       setError(apiError);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   if (loading) {
//     return (
//       <div style={styles.container}>
//         <div style={styles.formContainer}>
//           <Loader2 size={32} style={styles.spinner} />
//           <p>Verifying invitation...</p>
//         </div>
//       </div>
//     );
//   }

//   if (success) {
//     return (
//       <div style={styles.container}>
//         <div style={styles.successContainer}>
//           <CheckCircle size={48} style={styles.successIcon} />
//           <h2>Registration Successful!</h2>
//           <p>You will be redirected to the login page shortly.</p>
//         </div>
//       </div>
//     );
//   }

//   if (error && !invitationDetails) {
//     return (
//       <div style={styles.container}>
//         <div style={styles.formContainer}>
//           <h2>Invalid Invitation</h2>
//           <p style={styles.error}>{error}</p>
//           <Link to="/" style={styles.link}>
//             Go to Home
//           </Link>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div style={styles.container}>
//       <div style={styles.formContainer}>
//         <div style={styles.header}>
//           <h1 style={styles.title}>AcademiaSpace</h1>
//           <p style={styles.subtitle}>Create your account</p>
//         </div>

//         {invitationDetails && (
//           <div style={styles.invitationInfo}>
//             <p>
//               Accepting invitation to join <strong>{invitationDetails.research_group?.name}</strong>.
//             </p>
//             {invitationDetails.message && <p style={styles.invitationMessage}>"{invitationDetails.message}"</p>}
//           </div>
//         )}

//         <form onSubmit={handleSubmit} style={styles.form}>
//           <div style={styles.nameRow}>
//             <div style={styles.inputGroup}>
//               <label htmlFor="first_name" style={styles.label}>
//                 First Name
//               </label>
//               <input
//                 id="first_name"
//                 name="first_name"
//                 type="text"
//                 value={formData.first_name}
//                 onChange={handleChange}
//                 placeholder="Enter your first name"
//                 required
//                 disabled={isLoading}
//                 style={styles.input}
//               />
//             </div>
//             <div style={styles.inputGroup}>
//               <label htmlFor="last_name" style={styles.label}>
//                 Last Name
//               </label>
//               <input
//                 id="last_name"
//                 name="last_name"
//                 type="text"
//                 value={formData.last_name}
//                 onChange={handleChange}
//                 placeholder="Enter your last name"
//                 required
//                 disabled={isLoading}
//                 style={styles.input}
//               />
//             </div>
//           </div>

//           <div style={styles.inputGroup}>
//             <label htmlFor="username" style={styles.label}>
//               Username
//             </label>
//             <input
//               id="username"
//               name="username"
//               type="text"
//               value={formData.username}
//               onChange={handleChange}
//               placeholder="Choose a username"
//               required
//               disabled={isLoading}
//               style={styles.input}
//               autoComplete="username"
//             />
//           </div>

//           <div style={styles.inputGroup}>
//             <label htmlFor="email" style={styles.label}>
//               Email
//             </label>
//             <input
//               id="email"
//               name="email"
//               type="email"
//               value={formData.email}
//               onChange={handleChange}
//               placeholder="Enter your email"
//               required
//               disabled={true} // Email is pre-filled and disabled
//               style={{ ...styles.input, backgroundColor: "#f9fafb" }}
//               autoComplete="email"
//             />
//           </div>

//           <div style={styles.inputGroup}>
//             <label htmlFor="password" style={styles.label}>
//               Password
//             </label>
//             <div style={styles.passwordContainer}>
//               <input
//                 id="password"
//                 name="password"
//                 type={showPassword ? "text" : "password"}
//                 value={formData.password}
//                 onChange={handleChange}
//                 placeholder="Create a password (min. 8 characters)"
//                 required
//                 disabled={isLoading}
//                 style={{ ...styles.input, paddingRight: "45px" }}
//                 autoComplete="new-password"
//               />
//               <button
//                 type="button"
//                 onClick={() => setShowPassword(!showPassword)}
//                 style={styles.passwordToggle}
//                 disabled={isLoading}
//               >
//                 {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
//               </button>
//             </div>
//           </div>

//           <div style={styles.inputGroup}>
//             <label htmlFor="confirmPassword" style={styles.label}>
//               Confirm Password
//             </label>
//             <div style={styles.passwordContainer}>
//               <input
//                 id="confirmPassword"
//                 name="confirmPassword"
//                 type={showConfirmPassword ? "text" : "password"}
//                 value={formData.confirmPassword}
//                 onChange={handleChange}
//                 placeholder="Confirm your password"
//                 required
//                 disabled={isLoading}
//                 style={{ ...styles.input, paddingRight: "45px" }}
//                 autoComplete="new-password"
//               />
//               <button
//                 type="button"
//                 onClick={() => setShowConfirmPassword(!showConfirmPassword)}
//                 style={styles.passwordToggle}
//                 disabled={isLoading}
//               >
//                 {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
//               </button>
//             </div>
//           </div>

//           {error && (
//             <div style={styles.errorContainer}>
//               <p style={styles.error}>{error}</p>
//             </div>
//           )}

//           <button
//             type="submit"
//             disabled={isLoading}
//             style={{
//               ...styles.button,
//               ...(isLoading && styles.buttonLoading),
//             }}
//           >
//             {isLoading ? (
//               <>
//                 <Loader2 size={20} style={styles.spinner} />
//                 Creating Account...
//               </>
//             ) : (
//               <>
//                 <UserPlus size={20} />
//                 Create Account
//               </>
//             )}
//           </button>

//           <div style={styles.footer}>
//             <p style={styles.footerText}>
//               Already have an account?{" "}
//               <Link to="/login" style={styles.link}>
//                 Sign in here
//               </Link>
//             </p>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default RegistrationPage;
