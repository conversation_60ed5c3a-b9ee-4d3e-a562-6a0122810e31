import React from "react";
import { useAuth } from "../context/AuthContext";

const DebugUserPage = () => {
  const { user, userToken, loading } = useAuth();

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h1>🔍 Debug User Information</h1>
      
      <div style={{ marginBottom: "20px" }}>
        <h2>Loading State:</h2>
        <pre>{JSON.stringify({ loading }, null, 2)}</pre>
      </div>

      <div style={{ marginBottom: "20px" }}>
        <h2>User Token (first 50 chars):</h2>
        <pre>{userToken ? userToken.substring(0, 50) + "..." : "null"}</pre>
      </div>

      <div style={{ marginBottom: "20px" }}>
        <h2>User Object:</h2>
        <pre>{JSON.stringify(user, null, 2)}</pre>
      </div>

      <div style={{ marginBottom: "20px" }}>
        <h2>Permission Checks:</h2>
        <pre>{JSON.stringify({
          can_supervise: user?.can_supervise,
          can_invite: user?.can_invite,
          primary_role: user?.primary_role,
          isAdmin: user?.can_supervise || user?.can_invite || 
                   user?.primary_role === 'Administrator' || user?.primary_role === 'Professor'
        }, null, 2)}</pre>
      </div>

      <div style={{ marginBottom: "20px" }}>
        <h2>All User Properties:</h2>
        <pre>{user ? Object.keys(user).map(key => `${key}: ${user[key]}`).join('\n') : 'null'}</pre>
      </div>
    </div>
  );
};

export default DebugUserPage;
