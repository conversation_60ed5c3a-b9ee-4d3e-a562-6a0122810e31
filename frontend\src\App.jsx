// frontend/src/App.jsx

import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";

// Import Layouts
import StudentLayout from "./layouts/StudentLayout";
import AdminLayout from "./layouts/AdminLayout";

// Import Pages
import LoginPage from "./pages/LoginPage";
import RegistrationPage from "./pages/RegistrationPage"; // <-- IMPORT THE REGISTRATION PAGE
import AuthRedirect from "./pages/AuthRedirect";
import StudentDashboard from "./pages/StudentDashboard";
import AdminDashboard from "./pages/AdminDashboard";
import ProfessorDashboard from "./pages/ProfessorDashboard";
import ProjectsListPage from "./pages/ProjectsListPage";

// Import Protected Route
import ProtectedRoute from "./components/ProtectedRoute";

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* ======================================================= */}
          {/* SECTION 1: PUBLIC ROUTES (No login required)          */}
          {/* ======================================================= */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegistrationPage />} /> {/* <-- THIS IS THE FIX */}
          {/* ======================================================= */}
          {/* SECTION 2: PROTECTED ROUTES (Login required)          */}
          {/* ======================================================= */}
          {/* Post-login Redirector (Handles where to send logged-in users) */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <AuthRedirect />
              </ProtectedRoute>
            }
          />
          {/* Student-specific Routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <StudentLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<StudentDashboard />} />
            <Route path="projects" element={<ProjectsListPage />} />
            {/* e.g., <Route path="papers" element={<PapersListPage />} /> */}
          </Route>
          {/* Admin-specific Routes */}
          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <AdminLayout />
              </ProtectedRoute>
            }
          >
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="projects" element={<ProjectsListPage />} />
            {/* e.g., <Route path="users" element={<UserManagementPage />} /> */}
          </Route>
          {/* Professor-specific Routes */}
          <Route
            path="/professor"
            element={
              <ProtectedRoute>
                <AdminLayout />
              </ProtectedRoute>
            }
          >
            <Route path="dashboard" element={<ProfessorDashboard />} />
          </Route>
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;

