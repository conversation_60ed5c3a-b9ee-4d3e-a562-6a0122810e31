// frontend/src/components/ProjectCard.jsx

import React from "react";
import { Link } from "react-router-dom";

const ProjectCard = ({ project }) => {
  // A simple guard clause in case project data is missing
  if (!project) return null;

  return (
    // The entire card is a link to the project's detail page
    <Link to={`/projects/${project.id}`} style={styles.projectCardLink}>
      <div style={styles.projectCard}>
        <h3 style={styles.title}>{project.title}</h3>
        <p style={styles.description}>{project.description}</p>
        <div style={styles.footer}>
          {/* We can use dummy values for now */}
          <span style={{ ...styles.badge, ...styles.type }}>{project.project_type || "RESEARCH"}</span>
          <span style={{ ...styles.badge, ...styles.status }}>{project.status || "IN_PROGRESS"}</span>
        </div>
      </div>
    </Link>
  );
};

// Styles for the card
const styles = {
  projectCardLink: {
    textDecoration: "none",
    color: "inherit",
  },
  projectCard: {
    background: "white",
    padding: "20px",
    borderRadius: "8px",
    boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
    transition: "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out",
    cursor: "pointer",
  },
  title: {
    margin: "0 0 10px 0",
  },
  description: {
    color: "#64748b",
    fontSize: "0.9rem",
    minHeight: "40px",
  },
  footer: {
    display: "flex",
    gap: "10px",
    alignItems: "center",
    marginTop: "20px",
    borderTop: "1px solid #f1f5f9",
    paddingTop: "15px",
  },
  badge: {
    padding: "4px 10px",
    borderRadius: "12px",
    fontSize: "0.75rem",
    fontWeight: "500",
    textTransform: "uppercase",
  },
  type: { background: "#eff6ff", color: "#1d4ed8" },
  status: { background: "#fefce8", color: "#a16207" },
};

// --- THIS IS THE CRITICAL LINE THAT WAS MISSING ---
export default ProjectCard;



