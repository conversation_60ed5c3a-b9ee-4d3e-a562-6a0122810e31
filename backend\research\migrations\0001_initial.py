# Generated by Django 5.2.6 on 2025-09-15 14:12

import django.contrib.postgres.indexes
import django.contrib.postgres.search
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Paper',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, max_length=500, null=True)),
                ('authors', models.JSONField(blank=True, default=list)),
                ('abstract', models.TextField(blank=True, null=True)),
                ('source_journal', models.CharField(blank=True, max_length=300, null=True)),
                ('publication_date', models.DateField(blank=True, null=True)),
                ('doi', models.CharField(blank=True, max_length=200, null=True)),
                ('url', models.URLField(blank=True, null=True)),
                ('access_type', models.CharField(choices=[('OPEN_ACCESS', 'Open Access'), ('PAYWALLED', 'Paywalled'), ('UNKNOWN', 'Unknown')], default='UNKNOWN', max_length=20)),
                ('status', models.CharField(choices=[('NEW', 'New'), ('REVIEWED', 'Reviewed'), ('RELEVANT', 'Relevant'), ('IRRELEVANT', 'Irrelevant')], default='NEW', max_length=15)),
                ('is_priority', models.BooleanField(default=False)),
                ('retrieved_date', models.DateField(auto_now_add=True)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('summary', models.TextField(blank=True, null=True)),
                ('confidence', models.CharField(choices=[('HIGH', 'High'), ('MEDIUM', 'Medium'), ('LOW', 'Low')], default='LOW', max_length=10)),
                ('confidence_reason', models.CharField(default='Initial assessment', max_length=255)),
                ('is_favorite', models.BooleanField(default=False)),
                ('citation_count', models.IntegerField(default=0)),
                ('search_vector', django.contrib.postgres.search.SearchVectorField(null=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='papers', to=settings.AUTH_USER_MODEL)),
                ('research_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='papers', to='accounts.researchgroup')),
            ],
            options={
                'ordering': ['-retrieved_date'],
            },
        ),
        migrations.CreateModel(
            name='Note',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to=settings.AUTH_USER_MODEL)),
                ('paper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='research.paper')),
            ],
            options={
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=500)),
                ('description', models.TextField(blank=True)),
                ('project_type', models.CharField(choices=[('THESIS', 'Thesis/Dissertation'), ('GRANT', 'Grant Proposal'), ('PAPER', 'Research Paper'), ('OTHER', 'Other')], default='PAPER', max_length=20)),
                ('content', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('IN_PROGRESS', 'In Progress'), ('SUBMITTED', 'Submitted'), ('PUBLISHED', 'Published'), ('COMPLETED', 'Completed')], default='DRAFT', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('research_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='accounts.researchgroup')),
            ],
            options={
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='ProjectMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_messages', to=settings.AUTH_USER_MODEL)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='research.project')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectParticipation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('role_in_project', models.CharField(choices=[('LEAD_RESEARCHER', 'Lead Researcher'), ('PRIMARY_SUPERVISOR', 'Primary Supervisor'), ('CO_SUPERVISOR', 'Co-Supervisor'), ('COLLABORATOR', 'Collaborator'), ('STUDENT', 'Student')], max_length=30)),
                ('date_added', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_participations', to=settings.AUTH_USER_MODEL)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participations', to='research.project')),
            ],
        ),
        migrations.AddIndex(
            model_name='paper',
            index=django.contrib.postgres.indexes.GinIndex(fields=['search_vector'], name='research_pa_search__1804f0_gin'),
        ),
        migrations.AddConstraint(
            model_name='paper',
            constraint=models.UniqueConstraint(condition=models.Q(('doi__isnull', False)), fields=('owner', 'doi'), name='unique_paper_per_user'),
        ),
        migrations.AlterUniqueTogether(
            name='note',
            unique_together={('paper', 'owner')},
        ),
        migrations.AlterUniqueTogether(
            name='projectparticipation',
            unique_together={('project', 'person')},
        ),
    ]
