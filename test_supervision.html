<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supervision API</title>
</head>
<body>
    <h1>Test Supervision Project Creation</h1>
    
    <div>
        <h2>Step 1: Login</h2>
        <input type="text" id="username" placeholder="Username" value="admin">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button onclick="login()">Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div>
        <h2>Step 2: Create Supervision Project</h2>
        <input type="text" id="studentId" placeholder="Student ID" value="6058a9e9-23d9-46e7-bc3d-52a102748476">
        <button onclick="createSupervision()">Create Supervision Project</button>
        <div id="supervisionResult"></div>
    </div>

    <script>
        let accessToken = null;

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access;
                    document.getElementById('loginResult').innerHTML = 
                        `<p style="color: green;">Login successful! Token: ${accessToken.substring(0, 50)}...</p>`;
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<p style="color: red;">Login failed: ${JSON.stringify(data)}</p>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<p style="color: red;">Login error: ${error.message}</p>`;
            }
        }

        async function createSupervision() {
            if (!accessToken) {
                document.getElementById('supervisionResult').innerHTML = 
                    '<p style="color: red;">Please login first!</p>';
                return;
            }
            
            const studentId = document.getElementById('studentId').value;
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/accounts/admin/start-supervision/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`
                    },
                    body: JSON.stringify({ student_id: studentId })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('supervisionResult').innerHTML = 
                        `<p style="color: green;">Supervision project created successfully!</p>
                         <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    document.getElementById('supervisionResult').innerHTML = 
                        `<p style="color: red;">Failed to create supervision project: ${JSON.stringify(data)}</p>`;
                }
            } catch (error) {
                document.getElementById('supervisionResult').innerHTML = 
                    `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
