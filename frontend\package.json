{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tiptap/extension-font-family": "^3.4.3", "@tiptap/extension-image": "^3.4.3", "@tiptap/extension-link": "^3.4.2", "@tiptap/extension-placeholder": "^3.4.2", "@tiptap/extension-table": "^3.4.3", "@tiptap/extension-table-cell": "^3.4.3", "@tiptap/extension-table-header": "^3.4.3", "@tiptap/extension-table-row": "^3.4.3", "@tiptap/extension-text-align": "^3.4.2", "@tiptap/extension-text-style": "^3.4.3", "@tiptap/extension-underline": "^3.4.2", "@tiptap/react": "^3.4.2", "@tiptap/starter-kit": "^3.4.2", "axios": "^1.12.1", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.9.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}