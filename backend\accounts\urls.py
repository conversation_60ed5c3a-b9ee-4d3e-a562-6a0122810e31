# backend/accounts/urls.py

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views
from .views import (
    StudentRegistrationViewSet,
    UserProfileViewSet,
    InstitutionViewSet,
    DepartmentViewSet,
    ResearchGroupViewSet,
    InvitationViewSet,
    RoleTypeViewSet,
    AdminDashboardViewSet # <-- Import our new viewset
)

# Create a router to automatically generate URLs for our ViewSets
router = DefaultRouter()

# Register the ViewSets that need standard CRUD URLs
router.register(r'users', UserProfileViewSet, basename='user-profile')
router.register(r'institutions', InstitutionViewSet, basename='institution')
router.register(r'departments', DepartmentViewSet, basename='department')
router.register(r'research-groups', ResearchGroupViewSet, basename='research-group')
router.register(r'invitations', InvitationViewSet, basename='invitation')
router.register(r'roles', RoleTypeViewSet, basename='role-type')

# --- THIS IS THE CRITICAL PART ---
# This registers the /api/accounts/admin/ endpoint and its actions (like 'members')
router.register(r'admin', AdminDashboardViewSet, basename='admin-dashboard')


urlpatterns = [
    # Include all the URLs automatically generated by the router
    path('', include(router.urls)),

    # Add the path for our custom student registration action
    path('register/student/', StudentRegistrationViewSet.as_view({'post': 'register'}), name='student-register'),

    # Public endpoint for validating invitation codes during registration
    path('invitations/validate/<uuid:code>/', views.validate_invitation_code, name='validate-invitation-code'),

    # Public endpoint for user registration with invitation codes
    path('register/', views.InvitedUserRegistrationView.as_view(), name='user-register'),
]








# # backend/accounts/urls.py

# from django.urls import path, include
# from rest_framework.routers import DefaultRouter
# from .views import (
#     InvitedUserRegistrationView,
#     AdminDashboardViewSet,
#     ResearchGroupViewSet,
#     InstitutionRegistrationView
# )

# # ==============================================================================
# # ROUTER CONFIGURATION
# # ==============================================================================

# # A router is used for ViewSets that provide standard CRUD-like operations.
# router = DefaultRouter()

# # This will create endpoints like:
# # /api/auth/groups/ (GET, POST)
# # /api/auth/groups/{id}/ (GET)
# # /api/auth/groups/{id}/invite/ (POST)
# router.register(r'groups', ResearchGroupViewSet, basename='group')

# # ==============================================================================
# # FINAL URL PATTERNS
# # ==============================================================================

# urlpatterns = [
#     # --- Registration ---
#     # POST /api/auth/register/
#     path('register/', InvitedUserRegistrationView.as_view(), name='api-register'),
    
#     # --- Admin/Supervisor Dashboard ---
#     # Since AdminDashboardViewSet uses custom actions, we define its URLs manually for clarity.
#     # GET /api/auth/admin/members/
#     path(
#         'admin/members/', 
#         AdminDashboardViewSet.as_view({'get': 'members'}), 
#         name='admin-members'
#     ),
#     # You can add the 'stats' endpoint here in the same way when you build it.

#     path('institutions/register/', InstitutionRegistrationView.as_view(), name='api-institution-register'),
    
    
#     # --- Router-Generated URLs ---
#     # This includes all the URLs for the ResearchGroupViewSet.
#     path('', include(router.urls)),
# ]





# # AcademiaSpace/backend/accounts/urls.py

# from django.urls import path, include
# from rest_framework.routers import DefaultRouter
# from . import views

# # Create a router and register our viewsets with it
# router = DefaultRouter()
# router.register(r'research-groups', views.ResearchGroupViewSet, basename='researchgroup')
# router.register(r'invitations', views.InvitationViewSet, basename='invitation')
# router.register(r'admin-dashboard', views.AdminDashboardViewSet, basename='admindashboard')

# # The API URLs are now determined automatically by the router
# urlpatterns = [
#     # Authentication endpoints
#     path('register/', views.InvitedUserRegistrationView.as_view(), name='invited-user-register'),
#     path('validate-invitation/<uuid:token>/', views.validate_invitation_token, name='validate-invitation-token'),
    
#     # Include all router URLs
#     path('', include(router.urls)),
# ]



