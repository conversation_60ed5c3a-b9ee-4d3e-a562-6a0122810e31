# AcademiaSpace/backend/accounts/views.py

from rest_framework import generics, permissions, viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from .models import CustomUser, ResearchGroup, RoleType, Institution, ResearchGroupMembership
from .serializers import (
    StudentRegistrationSerializer,
    InstitutionRegistrationSerializer,
    SimpleUserSerializer,
    UserDetailSerializer, RoleTypeSerializer
)
from .permissions import IsWorkspaceAdmin, IsInstitutionAdmin

import logging
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from rest_framework import viewsets, permissions, status, generics, mixins
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.exceptions import PermissionDenied, ValidationError

# --- App-Specific Imports ---
from .models import ResearchGroup, Invitation, CustomUser, ResearchGroupMembership
from .serializers import (
    InvitedUserRegistrationSerializer,
    InvitationCreateSerializer,
    InvitationSerializer,
    SimpleResearchGroupSerializer,
    SimpleUserSerializer,
    InvitationAcceptSerializer,
    InstitutionRegistrationSerializer
)
from .permissions import IsGroupAdmin, CanInviteMembers

# --- THIS IS THE FIX ---
# Add RoleTypeSerializer to this import list.
from .serializers import (
    InstitutionSerializer, DepartmentSerializer, ResearchGroupSerializer,
    RoleTypeSerializer, UserDetailSerializer, MyTokenObtainPairSerializer,
    StudentRegistrationSerializer, InvitationSerializer, SimpleUserSerializer, 
    RoleTypeSerializer, UserRoleSerializer, ResearchGroupMembershipSerializer, 
   InstitutionRegistrationSerializer, InvitationAcceptSerializer, InstitutionSerializer 
)
from .permissions import IsInstitutionAdmin, IsDepartmentAdmin, IsGroupAdmin

from .serializers import StudentRegistrationSerializer

from .serializers import UserDetailSerializer # <-- Make sure this is imported
from .models import CustomUser


from .serializers import InstitutionSerializer # <-- Make sure this is imported
from .models import Institution # <-- Make sure this is imported
from .permissions import IsInstitutionAdmin # <-- Make sure this is imported


from .serializers import DepartmentSerializer # <-- Make sure this is imported
from .models import Department # <-- Make sure this is imported
from .permissions import IsInstitutionAdmin # <-- Make sure this is imported

from .models import CustomUser, ResearchGroup, ResearchGroupMembership 
from .serializers import SimpleUserSerializer, SimpleResearchGroupSerializer
from .permissions import IsGroupAdmin


from research.models import Project, ProjectParticipation # <-- Import Project models
from research.serializers import ProjectSerializer

# Set up logging
logger = logging.getLogger(__name__)

# ==============================================================================
# AUTHENTICATION & REGISTRATION VIEWS
# ==============================================================================

class InvitedUserRegistrationView(generics.CreateAPIView):
    """
    An endpoint for new users to register using a valid invitation token.
    This is publicly accessible.
    """
    serializer_class = InvitedUserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """
        Override create to handle custom invitation logic and error handling.
        """
        try:
            response = super().create(request, *args, **kwargs)
            
            # Log successful registration
            if response.status_code == status.HTTP_201_CREATED:
                logger.info(f"New user registered via invitation: {response.data.get('email')}")
            
            return response
            
        except ValidationError as e:
            logger.warning(f"Invitation registration validation error: {e}")
            return Response(
                {"error": "Invalid registration data", "details": e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error during invitation registration: {e}")
            return Response(
                {"error": "An unexpected error occurred during registration"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def validate_invitation_code(request, code):
    """
    Validate an invitation code before registration.
    This endpoint is publicly accessible for the registration process.
    """
    try:
        invitation = get_object_or_404(Invitation, code=code)

        if invitation.status != Invitation.Status.PENDING:
            return Response(
                {"valid": False, "error": "This invitation has already been used or revoked."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not invitation.is_valid():
            return Response(
                {"valid": False, "error": "This invitation has expired."},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({
            "valid": True,
            "invitee_email": invitation.invitee_email,
            "intended_role": {
                "id": invitation.intended_role.id,
                "name": invitation.intended_role.name
            },
            "research_group": {
                "id": invitation.research_group.id,
                "name": invitation.research_group.name
            } if invitation.research_group else None,
            "inviter": {
                "display_name": invitation.invited_by.get_full_name() or invitation.invited_by.username
            }
        })

    except Exception as e:
        logger.error(f"Error validating invitation code {code}: {e}")
        return Response(
            {"valid": False, "error": "Invalid invitation code."},
            status=status.HTTP_400_BAD_REQUEST
        )


# ==============================================================================
# RESEARCH GROUP VIEWS
# ==============================================================================

class RoleTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A read-only API endpoint for retrieving available role types.
    """
    serializer_class = RoleTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = RoleType.objects.all()


class ResearchGroupViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Provides a read-only API endpoint for a user to see the
    Research Groups they are a member of.
    """
    serializer_class = SimpleResearchGroupSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        --- CORRECTED AND SIMPLIFIED QUERY ---
        This is the core logic: a user can only see the research groups
        where they have an active membership.
        """
        user = self.request.user
        
        # We query directly through the user's 'group_memberships' related manager.
        # This is the most direct and idiomatic Django way to do this.
        return ResearchGroup.objects.filter(
            memberships__person=user, 
            memberships__status=ResearchGroupMembership.Status.ACTIVE
        ).distinct()


# ==============================================================================
# ADMIN & GROUP MANAGEMENT VIEWS
# ==============================================================================

class AdminDashboardViewSet(viewsets.ViewSet):
    """
    A collection of read-only endpoints to power the Admin/Supervisor Dashboard.
    """
    permission_classes = [IsGroupAdmin] # Ensures only users with invite/supervise permissions can access

    @action(detail=False, methods=['get'])
    def overview(self, request):
        """
        Provides all the necessary data for the admin dashboard in a single call.
        """
        user = request.user
        
        # Find the primary research group for this admin.
        # We'll assume for now it's their first active group.
        primary_membership = ResearchGroupMembership.objects.filter(
            person=user, status=ResearchGroupMembership.Status.ACTIVE
        ).select_related('research_group').first()

        if not primary_membership:
            return Response(
                {"detail": "You are not an active member of any research groups."},
                status=status.HTTP_404_NOT_FOUND
            )
        
        research_group = primary_membership.research_group

        # --- Aggregate Statistics ---
        stats = {
            'total_students': ResearchGroupMembership.objects.filter(
                research_group=research_group,
                status=ResearchGroupMembership.Status.ACTIVE,
                person__primary_role__name='Student'
            ).count(),
            'active_projects': 0, # Placeholder until Project model is used
            'pending_invitations': Invitation.objects.filter(
                research_group=research_group,
                status=Invitation.Status.PENDING
            ).count(),
        }
        
        # --- List of Members ---
        memberships = ResearchGroupMembership.objects.filter(
            research_group=research_group, status=ResearchGroupMembership.Status.ACTIVE
        ).select_related('person', 'person__primary_role')
        
        members = [m.person for m in memberships]

        return Response({
            'research_group': SimpleResearchGroupSerializer(research_group).data,
            'stats': stats,
            'members': SimpleUserSerializer(members, many=True).data
        })

    @action(detail=False, methods=['get'])
    def members(self, request):
        """Returns a list of all members in the admin's research groups."""
        user = request.user

        # Find the primary research group for this admin
        primary_membership = ResearchGroupMembership.objects.filter(
            person=user, status=ResearchGroupMembership.Status.ACTIVE
        ).select_related('research_group').first()

        if not primary_membership:
            return Response(
                {"detail": "You are not an active member of any research groups."},
                status=status.HTTP_404_NOT_FOUND
            )

        research_group = primary_membership.research_group

        # Get all members of this research group
        memberships = ResearchGroupMembership.objects.filter(
            research_group=research_group,
            status=ResearchGroupMembership.Status.ACTIVE
        ).select_related('person', 'person__primary_role')

        members = [m.person for m in memberships]

        return Response(SimpleUserSerializer(members, many=True).data)


    # --- THIS IS THE CORRECTED ACTION ---
    @action(detail=False, methods=['post'], url_path='start-supervision')
    def start_supervision(self, request):
        """
        Creates a new 'SUPERVISION' type Project for a given student,
        linking them with the current admin user as the supervisor.
        """
        supervisor = request.user
        student_id = request.data.get('student_id')

        if not student_id:
            return Response({"error": "Student ID is required."}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            student = CustomUser.objects.get(id=student_id, roles__role_type__name='Student')
        except CustomUser.DoesNotExist:
            return Response({"error": "A valid student with that ID was not found."}, status=status.HTTP_404_NOT_FOUND)

        # Ensure the supervisor is part of a research group
        primary_membership = supervisor.group_memberships.filter(status='ACTIVE').first()
        if not primary_membership:
            return Response({"error": "Supervisor must be an active member of a research group."}, status=status.HTTP_400_BAD_REQUEST)
        
        # --- The Core Logic ---
        # 1. Create the new Project
        project_title = f"Supervision Project: {student.get_full_name() or student.username}"
        new_project = Project.objects.create(
            title=project_title,
            project_type=Project.ProjectType.SUPERVISION,
            research_group=primary_membership.research_group,
            status=Project.Status.IN_PROGRESS
        )

        # 2. Add the Supervisor as a participant
        ProjectParticipation.objects.create(
            project=new_project,
            person=supervisor,
            role_in_project=ProjectParticipation.ProjectRole.PRIMARY_SUPERVISOR,
            is_active=True, can_edit=True, can_invite=True
        )

        # 3. Add the Student as a participant
        ProjectParticipation.objects.create(
            project=new_project,
            person=student,
            role_in_project=ProjectParticipation.ProjectRole.STUDENT,
            is_active=True
        )
        
        # 4. Return the data for the new project
        serializer = ProjectSerializer(new_project)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


# ==============================================================================
# INVITATION MANAGEMENT VIEWS
# ==============================================================================

class InvitationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing invitations.
    """
    serializer_class = InvitationSerializer
    permission_classes = [permissions.IsAuthenticated, CanInviteMembers]

    def get_serializer_class(self):
        if self.action == 'create':
            return InvitationCreateSerializer
        return InvitationSerializer

    def get_queryset(self):
        """Users can only see invitations they created or for their groups."""
        user = self.request.user

        # Get groups where user has invitation permissions
        # A user can see invitations for groups they are an active member of
        manageable_groups = ResearchGroup.objects.filter(
            memberships__person=user,
            memberships__status=ResearchGroupMembership.Status.ACTIVE
        )

        return Invitation.objects.filter(
            Q(invited_by=user) | Q(research_group__in=manageable_groups)
        ).order_by('-created_at')

    def perform_create(self, serializer):
        """Set the invited_by field to the current user."""
        serializer.save(invited_by=self.request.user)

    @action(detail=True, methods=['post'])
    def resend(self, request, pk=None):
        """Resend an invitation email."""
        invitation = self.get_object()
        
        # Check permissions
        if not (invitation.invited_by == request.user or 
                invitation.research_group.can_user_manage(request.user)):
            raise PermissionDenied("You don't have permission to resend this invitation.")
        
        if invitation.status != Invitation.Status.PENDING:
            return Response(
                {"error": "Only pending invitations can be resent."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if invitation.is_expired():
            # Create a new invitation instead
            new_invitation = Invitation.objects.create(
                invitee_email=invitation.invitee_email,
                invited_by=request.user,
                research_group=invitation.research_group,
                role=invitation.role,
                message=invitation.message
            )
            
            # Send email for new invitation
            self._send_invitation_email(new_invitation)
            
            # Mark old invitation as replaced
            invitation.status = Invitation.Status.REVOKED
            invitation.save()
            
            return Response(
                InvitationSerializer(new_invitation).data,
                status=status.HTTP_201_CREATED
            )
        
        else:
            # Resend existing invitation
            self._send_invitation_email(invitation)
            return Response({"message": "Invitation resent successfully."})

    def _send_invitation_email(self, invitation):
        """Helper method to send invitation email."""
        # Similar implementation to ResearchGroupViewSet._send_invitation_email
        pass



class RegistrationAPIView(generics.CreateAPIView):
    """
    Public API endpoint for a new STUDENT to register using an invitation code.
    THIS IS THE CORRECT NAME.
    """
    serializer_class = StudentRegistrationSerializer
    permission_classes = [permissions.AllowAny]



# --- ADD THIS NEW VIEW ---
class InstitutionRegistrationView(generics.CreateAPIView):
    serializer_class = InstitutionRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            user = serializer.save()
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED,
                headers=self.get_success_headers(serializer.data)
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )



class UserViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A read-only API endpoint for retrieving user profiles.
    """
    queryset = CustomUser.objects.all()
    serializer_class = UserDetailSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # A user can only see their own profile, unless they are a superuser
        if self.request.user.is_superuser:
            return CustomUser.objects.all()
        return CustomUser.objects.filter(pk=self.request.user.pk)
    


# accounts/views.py
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework import status
from rest_framework.response import Response
from .serializers import MyTokenObtainPairSerializer



@method_decorator(csrf_exempt, name='dispatch')
class MyTokenObtainPairView(TokenObtainPairView):
    serializer_class = MyTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            return Response({
                'error': 'Invalid credentials',
                'detail': str(e)
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        return Response(serializer.validated_data, status=status.HTTP_200_OK)



class StudentRegistrationViewSet(viewsets.GenericViewSet):
    """
    Handles the registration of a new student using an invitation code.
    """
    serializer_class = StudentRegistrationSerializer
    permission_classes = [permissions.AllowAny] # Anyone can attempt to register

    @action(detail=False, methods=['post'])
    def register(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        return Response({
            "message": "Registration successful. Please log in."
        }, status=status.HTTP_201_CREATED)



class UserProfileViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A read-only endpoint for fetching the profile of the currently
    logged-in user.
    """
    serializer_class = UserDetailSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        This is the most robust way to do this: return a queryset
        containing only the currently authenticated user.
        """
        return CustomUser.objects.filter(pk=self.request.user.pk)
    


class InstitutionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing Institutions.
    This is a highly privileged endpoint, restricted to superusers
    or users with a specific 'Institution Administrator' role.
    """
    queryset = Institution.objects.all()
    serializer_class = InstitutionSerializer
    permission_classes = [IsInstitutionAdmin]



class DepartmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing Departments within an Institution.
    """
    serializer_class = DepartmentSerializer
    permission_classes = [IsInstitutionAdmin] # Only institution admins can create departments

    def get_queryset(self):
        """
        Admins can only see and manage departments within their own institution.
        """
        # We find the institution associated with the admin's primary department.
        if self.request.user.primary_department:
            return Department.objects.filter(institution=self.request.user.primary_department.institution)
        return Department.objects.none() # Return empty if user has no department
    
    def perform_create(self, serializer):
        """
        Automatically assign the correct institution when a new department is created.
        """
        if self.request.user.primary_department:
            serializer.save(institution=self.request.user.primary_department.institution)







