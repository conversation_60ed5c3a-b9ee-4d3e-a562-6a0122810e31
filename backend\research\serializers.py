# backend/research/serializers.py

from rest_framework import serializers
from .models import Paper, Note, Project, ProjectParticipation, ProjectMessage
from accounts.models import CustomUser, ResearchGroup



# ==============================================================================
# SECTION 1: REUSABLE HELPER SERIALIZERS
# ==============================================================================

class SimpleUserSerializer(serializers.ModelSerializer):
    """A simple, read-only serializer for displaying basic user info."""
    class Meta:
        model = CustomUser
        fields = ['id', 'username', 'first_name', 'last_name', 'primary_role']



class SimpleResearchGroupSerializer(serializers.ModelSerializer):
    """A simple serializer for displaying a research group's name."""
    class Meta:
        model = ResearchGroup
        fields = ['id', 'name']



# ==============================================================================
# SECTION 2: DASHBOARD-SPECIFIC SERIALIZERS
# ==============================================================================

class DashboardStatsSerializer(serializers.Serializer):
    """
    A specific serializer for the aggregated stats cards on the dashboard.
    This is a non-model serializer, as it combines data from multiple sources.
    """
    active_projects = serializers.IntegerField()
    publications = serializers.IntegerField()
    collaborators = serializers.IntegerField()
    citations = serializers.IntegerField()



class DashboardPaperSerializer(serializers.ModelSerializer):
    """
    A lean serializer for showing recent papers on the dashboard.
    """
    class Meta:
        model = Paper
        fields = ['id', 'title', 'source_journal', 'publication_date', 'status']



class DashboardProjectSerializer(serializers.ModelSerializer):
    """
    A serializer for the "Active Projects" list on the dashboard.
    It includes just enough information for the summary card.
    """
    # We can add a simple completion percentage for the UI
    completion_percentage = serializers.SerializerMethodField()
    research_group_name = serializers.CharField(source='research_group.name', read_only=True)

    class Meta:
        model = Project
        fields = [
            'id', 'title', 'description', 'status',
            'completion_percentage', 'research_group_name', 'content'
        ]

    def get_completion_percentage(self, obj):
        # This is a placeholder for more complex logic you could add later
        # For now, we can return a static value or a simple calculation.
        if obj.status == Project.Status.COMPLETED:
            return 100
        return 50 # Placeholder value



class DashboardPublicationSerializer(serializers.ModelSerializer):
    """
    A serializer for the "Recent Publications" list on the dashboard.
    This assumes a "publication" is a Project with a status of 'PUBLISHED'.
    """
    # We can pull the citation count from a related Paper model if you link them,
    # or just use a placeholder for now.
    citations = serializers.SerializerMethodField()

    class Meta:
        model = Project # A publication is a type of project
        fields = ['id', 'title', 'publication_date', 'citations']

    def get_citations(self, obj):
        # Placeholder - you would implement logic to get citation data here
        return 0



class RecentActivitySerializer(serializers.Serializer):
    """
    A generic serializer for the "Recent Activity" feed.
    This is a non-model serializer that standardizes different event types.
    """
    id = serializers.UUIDField()
    type = serializers.CharField() # e.g., 'project', 'publication', 'collaboration'
    description = serializers.CharField()
    time_since = serializers.CharField(source='timesince') # Django's 'timesince' utility
    timestamp = serializers.DateTimeField()



class UpcomingDeadlineSerializer(serializers.Serializer):
    """
    A generic serializer for the "Upcoming Deadlines" list.
    """
    id = serializers.UUIDField()
    title = serializers.CharField()
    project_name = serializers.CharField(source='project.title')
    due_date = serializers.DateField()


# ==============================================================================
# SECTION 3: FULL, DETAILED SERIALIZERS (for other parts of the app)
# ==============================================================================
class PaperSerializer(serializers.ModelSerializer):
    """Full serializer for a single academic Paper object."""
    owner = SimpleUserSerializer(read_only=True)
    research_group = SimpleResearchGroupSerializer(read_only=True)
    
    class Meta:
        model = Paper
        # We explicitly list fields for clarity and security.
        fields = [
            'id', 'owner', 'research_group', 'title', 'authors', 'abstract',
            'source_journal', 'publication_date', 'doi', 'arxiv_id', 'pmid', 'url',
            'access_type', 'status', 'is_priority', 'retrieved_date', 'tags',
            'summary', 'confidence', 'confidence_reason', 'is_favorite', 'citation_count',
            'keywords'
        ]
        read_only_fields = ['id', 'owner', 'research_group', 'retrieved_date']


class NoteSerializer(serializers.ModelSerializer):
    """Serializer for a user's private notes on a paper."""
    owner = SimpleUserSerializer(read_only=True)

    class Meta:
        model = Note
        fields = ['id', 'paper', 'owner', 'title', 'content', 'created_at', 'last_updated', 'is_public']
        # The 'paper' field is required for creation, but the owner is set by the system.
        read_only_fields = ['id', 'owner', 'created_at', 'last_updated']
        
        
# ==============================================================================
# SECTION 2: COLLABORATIVE PROJECT SERIALIZERS
# ==============================================================================

class ProjectParticipationSerializer(serializers.ModelSerializer):
    """Serializer for a participant within a project."""
    person = SimpleUserSerializer(read_only=True)

    class Meta:
        model = ProjectParticipation
        fields = ['id', 'person', 'role_in_project', 'date_added', 'is_active']



class ProjectSerializer(serializers.ModelSerializer):
    """A comprehensive serializer for a single Project."""
    research_group = serializers.PrimaryKeyRelatedField(
        queryset=ResearchGroup.objects.all(),
        required=False,
        allow_null=True
    )
    # Use the correct related_name 'participations'
    participations = ProjectParticipationSerializer(many=True, read_only=True)

    class Meta:
        model = Project
        fields = [
            'id', 'title', 'description', 'project_type', 'research_group',
            'content', 'status', 'created_at', 'last_updated', 'participations'
        ]
        read_only_fields = ['id', 'created_at', 'last_updated', 'participations']



class ProjectMessageSerializer(serializers.ModelSerializer):
    """Serializer for a message within a project chat."""
    author = SimpleUserSerializer(read_only=True)
    
    class Meta:
        model = ProjectMessage
        fields = ['id', 'author', 'content', 'reply_to', 'created_at', 'is_edited']
        # The 'project' is handled by the nested URL, and 'author' is set from the request user.
        read_only_fields = ['id', 'author', 'created_at', 'is_edited']



# Note: ProjectReference, ProjectAttachment, and ProjectInvitation serializers
# have been removed as the corresponding models don't exist yet.
# These can be added back when the models are implemented.
        
        
        



