# backend/academiaspace/urls.py

from django.contrib import admin
from django.urls import path, include
# Import our custom token view from the accounts app
from accounts.views import MyTokenObtainPairView
from rest_framework_simplejwt.views import TokenRefreshView

urlpatterns = [
    path('admin/', admin.site.urls),

    # --- API URLS ---
    # Include all URLs from the 'accounts' app under the '/api/accounts/' prefix
    path('api/accounts/', include('accounts.urls')),
    
    # Include all URLs from the 'research' app under the '/api/research/' prefix
    path('api/research/', include('research.urls')),

    # --- AUTHENTICATION URLS ---
    # Use our custom view for obtaining a token
    path('api/auth/token/', MyTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]



# # backend/academiaspace/urls.py

# from django.contrib import admin
# from django.urls import path, include
# from django.conf import settings
# from django.conf.urls.static import static
# from rest_framework_simplejwt.views import TokenRefreshView
# from accounts.views import TokenObtainPairView # Import your custom view

# urlpatterns = [
#     path('admin/', admin.site.urls),

#     # --- API URLS ---
#     # URLs for user/account management, registration, etc.
#     path('api/auth/', include('accounts.urls')),
    
#     path('api/accounts/', include('accounts.urls')),
    
#     # URLs for research-related models like Projects, Papers, etc.
#     path('api/', include('research.urls')),

#     # --- JWT TOKEN URLS ---
#     path('api/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
#     path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
# ]

# # This is for serving media files (like profile pictures) during development
# if settings.DEBUG:
#     urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


