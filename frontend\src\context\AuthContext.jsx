// frontend/src/context/AuthContext.jsx

import React, { createContext, useState, useContext, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import { apiClient, loginUser as apiLogin } from "../services/api";

// 1. Create the context itself
const AuthContext = createContext(null);

// 2. Create a custom hook for easy access to the context
export const useAuth = () => {
  return useContext(AuthContext);
};

// 3. Create the Provider component that will wrap your application
export const AuthProvider = ({ children }) => {
  const [userToken, setUserToken] = useState(localStorage.getItem("access_token"));
  const [user, setUser] = useState(null); // Will hold the decoded user info (id, role, etc.)
  const [loading, setLoading] = useState(true); // To handle the initial token check

  useEffect(() => {
    // This effect runs once when the app first loads
    const token = localStorage.getItem("access_token");
    if (token) {
      try {
        const decodedUser = jwtDecode(token);
        // Check if the token has expired
        if (decodedUser.exp * 1000 < Date.now()) {
          // Token is expired, log the user out
          logout();
        } else {
          // Token is valid, set the user state and the default auth header
          setUserToken(token);
          setUser(decodedUser);
          apiClient.defaults.headers.common["Authorization"] = `Bearer ${token}`;
        }
      } catch (error) {
        console.error("Invalid token found in storage", error);
        logout(); // If token is malformed, log out
      }
    }
    // Finished the initial check
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    // The 'login' function calls our API service
    const response = await apiLogin(username, password);
    const token = response.data.access;

    if (token) {
      // If login is successful, store the token and update the state
      localStorage.setItem("access_token", token);
      apiClient.defaults.headers.common["Authorization"] = `Bearer ${token}`;
      setUserToken(token);
      try {
        const decodedUser = jwtDecode(token);
        setUser(decodedUser);
      } catch (e) {
        console.error("Failed to decode new token on login", e);
      }
    }
    return response.data;
  };

  const logout = () => {
    // The 'logout' function clears everything
    localStorage.removeItem("access_token");
    // We should also remove the refresh token if we are using it
    localStorage.removeItem("refresh_token");
    delete apiClient.defaults.headers.common["Authorization"];
    setUserToken(null);
    setUser(null);
  };

  // The value that will be provided to all child components
  const contextValue = {
    userToken,
    user,
    loading,
    isAuthenticated: !!userToken,
    login,
    logout,
  };

  // We don't render the children until the initial loading is complete
  // to prevent UI flickers or incorrect redirects.
  return <AuthContext.Provider value={contextValue}>{!loading && children}</AuthContext.Provider>;
};


