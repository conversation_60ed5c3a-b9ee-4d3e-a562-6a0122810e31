# backend/research/urls.py

from django.urls import path, include
from rest_framework_nested import routers
from .views import (
    DashboardAPIView,
    RecentActivityAPIView,
    UpcomingDeadlinesAPIView,
    PaperViewSet,
    NoteViewSet,
    ProjectViewSet,
    ProjectParticipationViewSet,
    ProjectMessageViewSet
)

# 1. Create the top-level router for primary resources
router = routers.DefaultRouter()
router.register(r'papers', PaperViewSet, basename='paper')
router.register(r'projects', ProjectViewSet, basename='project')

# 2. Create nested routers for resources that belong to a parent

# e.g., /api/papers/{paper_pk}/notes/
papers_router = routers.NestedDefaultRouter(router, r'papers', lookup='paper')
papers_router.register(r'notes', NoteViewSet, basename='paper-notes')

# e.g., /api/projects/{project_pk}/participants/
projects_router = routers.NestedDefaultRouter(router, r'projects', lookup='project')
projects_router.register(r'participants', ProjectParticipationViewSet, basename='project-participants')
projects_router.register(r'messages', ProjectMessageViewSet, basename='project-messages')

# 3. Combine all URL patterns
urlpatterns = [
    path('', include(router.urls)),
    path('', include(papers_router.urls)),
    path('', include(projects_router.urls)),
    
    # Add your custom, non-router views
    path('dashboard/', DashboardAPIView.as_view(), name='api-dashboard'),
    path('activity/recent/', RecentActivityAPIView.as_view(), name='api-recent-activity'),
    path('deadlines/upcoming/', UpcomingDeadlinesAPIView.as_view(), name='api-upcoming-deadlines'),
]






