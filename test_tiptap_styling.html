<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TipTap Editor Styling Test</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            margin: 20px;
            background: #f8fafc;
        }
        
        /* TipTap Editor Styles */
        .tiptap-editor {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background-color: #fff;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 20px auto;
        }

        .editor-toolbar {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            padding: 0.75rem;
            border-bottom: 1px solid #d1d5db;
            background-color: #f3f4f6;
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding-right: 0.75rem;
            border-right: 1px solid #9ca3af;
        }

        .toolbar-group:last-child {
            border-right: none;
        }

        .toolbar-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: all 0.2s;
            font-weight: 500;
        }

        .toolbar-button:hover:not(:disabled) {
            background-color: #f3f4f6;
            border-color: #9ca3af;
            color: #111827;
        }

        .toolbar-button.active {
            background-color: #14b8a6;
            color: white;
            border-color: #14b8a6;
        }

        .toolbar-select {
            padding: 0.35rem 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background-color: #ffffff;
            font-size: 14px;
            color: #111827;
            cursor: pointer;
            font-weight: 500;
            min-width: 120px;
        }

        .toolbar-select:hover {
            border-color: #9ca3af;
            background-color: #f9fafb;
        }

        .editor-content {
            padding: 1rem;
            min-height: 200px;
            color: #374151;
        }

        .editor-footer {
            padding: 0.75rem 1rem;
            border-top: 1px solid #d1d5db;
            background-color: #f3f4f6;
            display: flex;
            justify-content: flex-end;
        }

        .character-count {
            font-size: 0.75rem;
            color: #374151;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <h1>TipTap Editor Styling Test</h1>
    <p>This shows how the TipTap Editor toolbar should look with darker, more visible styling:</p>
    
    <div class="tiptap-editor">
        <div class="editor-toolbar">
            <!-- Text style controls -->
            <div class="toolbar-group">
                <select class="toolbar-select">
                    <option value="paragraph">Paragraph</option>
                    <option value="1">Heading 1</option>
                    <option value="2">Heading 2</option>
                    <option value="3">Heading 3</option>
                </select>
            </div>

            <!-- Text Formatting -->
            <div class="toolbar-group">
                <button class="toolbar-button" title="Bold">
                    <strong>B</strong>
                </button>
                <button class="toolbar-button active" title="Italic">
                    <em>I</em>
                </button>
                <button class="toolbar-button" title="Underline">
                    <u>U</u>
                </button>
                <button class="toolbar-button" title="Strikethrough">
                    <s>S</s>
                </button>
            </div>

            <!-- Text Alignment -->
            <div class="toolbar-group">
                <button class="toolbar-button" title="Align Left">
                    ⬅
                </button>
                <button class="toolbar-button" title="Align Center">
                    ↔
                </button>
                <button class="toolbar-button" title="Align Right">
                    ➡
                </button>
                <button class="toolbar-button" title="Justify">
                    ⬌
                </button>
            </div>

            <!-- Lists and More -->
            <div class="toolbar-group">
                <button class="toolbar-button" title="Bullet List">
                    •
                </button>
                <button class="toolbar-button" title="Numbered List">
                    1.
                </button>
                <button class="toolbar-button" title="Quote">
                    "
                </button>
                <button class="toolbar-button" title="Code">
                    &lt;&gt;
                </button>
            </div>

            <!-- Actions -->
            <div class="toolbar-group">
                <button class="toolbar-button" title="Link">
                    🔗
                </button>
                <button class="toolbar-button" title="Image">
                    🖼
                </button>
                <button class="toolbar-button" title="Undo">
                    ↶
                </button>
                <button class="toolbar-button" title="Redo">
                    ↷
                </button>
            </div>
        </div>

        <div class="editor-content">
            <p>This is the editor content area. All toolbar buttons above should now be clearly visible with dark text and proper contrast.</p>
            <p><strong>Bold text</strong>, <em>italic text</em>, and <u>underlined text</u> should all be visible.</p>
        </div>

        <div class="editor-footer">
            <span class="character-count">156 characters</span>
        </div>
    </div>

    <script>
        // Add click handlers to demonstrate active states
        document.querySelectorAll('.toolbar-button').forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });
    </script>
</body>
</html>
