import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const AuthRedirect = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>; // Or a spinner
  }

  // Check user roles and redirect accordingly
  // Priority: Admin (can_invite) > Professor (can_supervise only) > Student
  const isAdmin = user && (
    user.can_invite ||
    user.primary_role === 'Administrator' ||
    user.primary_role === 'Institution Administrator'
  );

  const isProfessor = user && (
    user.primary_role === 'Professor' ||
    user.can_supervise
  ) && !isAdmin; // Only professor if not admin

  console.log("🔍 DEBUG AuthRedirect: User:", user);
  console.log("🔍 DEBUG AuthRedirect: Is admin?", isAdmin);
  console.log("🔍 DEBUG AuthRedirect: Is professor?", isProfessor);

  if (isAdmin) {
    // If they are an admin (can invite others), send them to the admin dashboard.
    return <Navigate to="/admin/dashboard" replace />;
  } else if (isProfessor) {
    // If they are a professor (can supervise but not invite), send them to the professor dashboard.
    return <Navigate to="/professor/dashboard" replace />;
  } else {
    // Otherwise, send them to the student dashboard.
    return <Navigate to="/dashboard" replace />;
  }
};

export default AuthRedirect;


