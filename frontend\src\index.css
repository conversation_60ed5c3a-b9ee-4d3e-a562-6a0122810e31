/* /frontend/src/index.css */

/* --- GLOBAL CSS VARIABLES --- */
:root {
    --primary: #4361ee;
    --secondary: #3f37c9;
    --success: #4cc9f0;
    --dark: #2b2d42;
    --light: #f8f9fa;
    --gray: #8d99ae;
    --border: #e9ecef;
    
    /* Variables for the new dashboard styles */
    --color-blue: #3b82f6;
    --color-green: #10b981;
    --color-purple: #8b5cf6;
    --color-orange: #f59e0b;
}

/* --- GLOBAL STYLES & RESETS --- */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f8fafc; /* A lighter background for the new dashboard */
    color: var(--dark);
}

/* Spinning animation for loading indicators */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
}

/* --- GLOBAL ANIMATIONS --- */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* --- OLD DASHBOARD STYLES (for PaperCard, etc.) --- */
/* We keep these as they are still used by your components */
header {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    padding: 30px 0;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 10px;
}

input, select, button {
    padding: 12px 15px;
    border: 1px solid var(--border);
    border-radius: 6px;
    font-size: 1rem;
}

button {
    background: var(--primary);
    color: white;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s;
}

button:hover {
    background: var(--secondary);
}

.paper-card {
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 20px;
    transition: transform 0.3s, box-shadow 0.3s;
    background-color: white;
}

.paper-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.paper-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.paper-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
}

.paper-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: var(--gray);
    margin-bottom: 15px;
}

.paper-abstract {
    margin-bottom: 15px;
    color: #555;
}

.paper-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
}

.detail-item h4 {
    font-size: 0.9rem;
    color: var(--gray);
    margin-bottom: 5px;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.tag {
    background: #e0e7ff;
    color: #4361ee;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.confidence-high { color: #4caf50; }
.confidence-medium { color: #ff9800; }
.confidence-low { color: #f44336; }

/* --- DASHBOARD LIST ITEM STYLES --- */
.dashboard-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.dashboard-list-item:last-child {
    border-bottom: none;
}

.dashboard-deadline-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.dashboard-deadline-item:last-child {
    border-bottom: none;
}

.dashboard-activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.dashboard-activity-item:last-child {
    border-bottom: none;
}

/* --- DASHBOARD INTERACTIVE ELEMENTS --- */
.dashboard-button {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 16px;
    background: white;
    color: #64748b;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dashboard-button:hover {
    border-color: #cbd5e1;
    color: #374151;
}

.dashboard-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.dashboard-link {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.dashboard-link:hover {
    text-decoration: underline;
}

.dashboard-card {
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.dashboard-quick-action {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.dashboard-quick-action:hover {
    border-color: #cbd5e1;
    background-color: #f8fafc;
}

.dashboard-icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    color: #3b82f6;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    background: none;
    cursor: pointer;
}

.dashboard-icon-button:hover {
    background-color: #eff6ff;
}


