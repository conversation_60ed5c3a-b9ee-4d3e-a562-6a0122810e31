# backend/research/views.py

import csv
import logging
from datetime import date, timedelta

from django.db.models import Q
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.core.files.storage import default_storage

from rest_framework.permissions import IsAuthenticated
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.exceptions import ValidationError, PermissionDenied
from rest_framework.parsers import MultiPartParser, FormParser

from .models import (
    Paper, Note, Project, ProjectParticipation, ProjectMessage
)
from accounts.models import CustomUser, ResearchGroup
from .serializers import (
    DashboardPaperSerializer, DashboardProjectSerializer, PaperSerializer,
    NoteSerializer, ProjectSerializer, ProjectParticipationSerializer,
    ProjectMessageSerializer, SimpleUserSerializer
)

logger = logging.getLogger(__name__)

# ==============================================================================
# REUSABLE PERMISSIONS (Good practice to keep them here if specific to this app)
# ==============================================================================

class IsProjectParticipant(permissions.BasePermission):
    """Ensures the user is an active participant in the project."""
    def has_object_permission(self, request, view, obj):
        project = obj if isinstance(obj, Project) else getattr(obj, 'project', None)
        if not project: return False
        return project.participations.filter(person=request.user, is_active=True).exists()



class IsProjectEditor(permissions.BasePermission):
    """Ensures the user has edit permissions for the project."""
    def has_object_permission(self, request, view, obj):
        project = obj if isinstance(obj, Project) else getattr(obj, 'project', None)
        if not project: return False
        participation = project.participations.filter(person=request.user, is_active=True).first()
        return participation and participation.can_edit

# ==============================================================================
# DASHBOARD & UTILITY VIEWS
# ==============================================================================

class DashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user

        # Fetch the user's 5 most recently updated active projects
        active_projects = Project.objects.filter(
            participations__person=user,
            participations__is_active=True,
            status=Project.Status.IN_PROGRESS
        ).order_by('-last_updated')[:5]

        # Fetch the user's 5 most recently collected papers
        recent_papers = Paper.objects.filter(
            owner=user # This is the key: only papers they own
        ).order_by('-retrieved_date')[:5]
        
        # Serialize the data
        projects_serializer = DashboardProjectSerializer(active_projects, many=True)
        papers_serializer = DashboardPaperSerializer(recent_papers, many=True)

        data = {
            "active_projects": projects_serializer.data,
            "recent_papers": papers_serializer.data,
        }
        return Response(data)
    

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_image_view(request):
    # This view for Tiptap image uploads is good as a standalone function.
    file_obj = request.FILES.get('image')
    if not file_obj: return Response({'error': 'No image file provided.'}, status=400)
    # ... (add more validation) ...
    file_path = f"tiptap_uploads/user_{request.user.id}/{file_obj.name}"
    saved_path = default_storage.save(file_path, file_obj)
    file_url = request.build_absolute_uri(default_storage.url(saved_path))
    return Response({'url': file_url}, status=status.HTTP_201_CREATED)

# ==============================================================================
# PRIMARY VIEWSETS (Top-Level Resources)
# ==============================================================================

class PaperViewSet(viewsets.ModelViewSet):
    """API endpoint for a user's private library of collected papers."""
    serializer_class = PaperSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Users can only see their own papers."""
        return Paper.objects.filter(owner=self.request.user)

    def perform_create(self, serializer):
        user = self.request.user
        primary_membership = user.group_memberships.filter(status='ACTIVE', is_primary=True).first()
        if not primary_membership:
            raise ValidationError("You must have a primary research group to add papers.")
        serializer.save(owner=user, research_group=primary_membership.research_group)


class ProjectViewSet(viewsets.ModelViewSet):
    """
    Handles creating, viewing, and managing research projects.
    """
    serializer_class = ProjectSerializer # Your existing, powerful serializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """A user can only see projects they are an active participant in."""
        return Project.objects.filter(
            participations__person=self.request.user,
            participations__is_active=True
        ).distinct().select_related('research_group')

    def perform_create(self, serializer):
        """
        When a user creates a new project, automatically:
        1. Assign it to their primary research group.
        2. Make them the "Lead Researcher" of the new project.
        """
        user = self.request.user
        
        # Find the user's primary research group
        primary_membership = user.group_memberships.filter(status='ACTIVE').first()
        if not primary_membership:
            raise PermissionDenied("You must be an active member of a research group to create a project.")
            
        research_group = primary_membership.research_group
        
        # Save the project with the research group assigned
        project = serializer.save(research_group=research_group)
        
        # Create the initial ProjectParticipation record for the creator
        ProjectParticipation.objects.create(
            project=project,
            person=user,
            role_in_project=ProjectParticipation.ProjectRole.LEAD_RESEARCHER,
            is_active=True,
            can_edit=True,
            can_invite=True
        )
        
        
# ==============================================================================
# NESTED VIEWSETS (Sub-Resources)
# ==============================================================================

class BaseNestedViewSet(viewsets.ModelViewSet):
    """A base class for viewsets nested under a parent resource."""
    permission_classes = [permissions.IsAuthenticated]

    def get_parent_object(self, parent_model, parent_kwarg_name):
        """Generic helper to get the parent object from the URL."""
        parent_pk = self.kwargs[parent_kwarg_name]
        try:
            return parent_model.objects.get(pk=parent_pk)
        except parent_model.DoesNotExist:
            raise Http404



class NoteViewSet(BaseNestedViewSet):
    """Nested endpoint for notes, accessed via /api/papers/{paper_pk}/notes/"""
    serializer_class = NoteSerializer

    def get_queryset(self):
        """Users can only see their own notes for the specific paper."""
        return Note.objects.filter(
            owner=self.request.user,
            paper__pk=self.kwargs['paper_pk']
        )

    def perform_create(self, serializer):
        paper = self.get_parent_object(Paper, 'paper_pk')
        if paper.owner != self.request.user:
            raise PermissionDenied("You can only create notes for your own papers.")
        serializer.save(owner=self.request.user, paper=paper)



class ProjectParticipationViewSet(BaseNestedViewSet):
    """Nested endpoint for participants, via /api/projects/{project_pk}/participants/"""
    serializer_class = ProjectParticipationSerializer

    def get_queryset(self):
        project = self.get_parent_object(Project, 'project_pk')
        return project.participations.filter(is_active=True)
    
    # Add perform_create/update logic here to manage inviting/changing roles



class ProjectMessageViewSet(BaseNestedViewSet):
    """Nested endpoint for messages, via /api/projects/{project_pk}/messages/"""
    serializer_class = ProjectMessageSerializer

    def get_queryset(self):
        project = self.get_parent_object(Project, 'project_pk')
        return project.messages.all().order_by('created_at')

    def perform_create(self, serializer):
        project = self.get_parent_object(Project, 'project_pk')
        if not project.participations.filter(person=self.request.user, is_active=True).exists():
            raise PermissionDenied("You must be a participant to send messages in this project.")
        serializer.save(project=project, author=self.request.user)
        




# # backend/research/views.py
# from rest_framework.views import APIView
# from rest_framework.response import Response
# from rest_framework.permissions import IsAuthenticated
# from .models import Paper, Project, ProjectMessage, ProjectParticipation
# from .serializers import DashboardPaperSerializer, DashboardProjectSerializer, ProjectSerializer, ProjectMessageSerializer
# from accounts.serializers import SimpleUserSerializer 
# from .models import Project, ProjectParticipation, CustomUser
# from .serializers import ProjectSerializer, ProjectParticipationSerializer
# from accounts.permissions import IsWorkspaceAdmin 



# class DashboardAPIView(APIView):
#     """
#     Gathers and serves all data for the main user dashboard in a single request.
#     """
#     permission_classes = [IsAuthenticated]

#     def get(self, request, *args, **kwargs):
#         user = request.user

#         # Fetch the user's 5 most recently collected papers
#         recent_papers = Paper.objects.filter(owner=user).order_by('-retrieved_date')[:5]

#         # Fetch the user's 5 most recently updated active projects
#         active_projects = Project.objects.filter(
#             participations__person=user,
#             participations__is_active=True,
#             status='IN_PROGRESS' # Or whichever statuses you consider "active"
#         ).order_by('-last_updated')[:5]

#         # Serialize all the data
#         user_serializer = SimpleUserSerializer(user)
#         papers_serializer = DashboardPaperSerializer(recent_papers, many=True)
#         projects_serializer = DashboardProjectSerializer(active_projects, many=True)

#         # Combine into a single response object
#         data = {
#             "user": user_serializer.data,
#             "recent_papers": papers_serializer.data,
#             "active_projects": projects_serializer.data,
#         }

#         return Response(data)


class RecentActivityAPIView(APIView):
    """
    Returns recent activity for the user.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # For now, return mock data. You can implement real activity tracking later
        from django.utils import timezone
        mock_activities = [
            {
                "id": "1",
                "type": "project",
                "description": "Updated project documentation",
                "time_since": "2 hours ago",
                "timestamp": timezone.now().isoformat()
            },
            {
                "id": "2",
                "type": "paper",
                "description": "Added new research paper",
                "time_since": "1 day ago",
                "timestamp": timezone.now().isoformat()
            }
        ]
        return Response(mock_activities)



class UpcomingDeadlinesAPIView(APIView):
    """
    Returns upcoming deadlines for the user.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        # For now, return mock data. You can implement real deadline tracking later
        mock_deadlines = [
            {
                "id": "1",
                "title": "Submit research proposal",
                "project_name": "AI Research Project",
                "due_date": "2025-09-20"
            },
            {
                "id": "2",
                "title": "Review paper draft",
                "project_name": "Machine Learning Study",
                "due_date": "2025-09-25"
            }
        ]
        return Response(mock_deadlines)


# class PaperListCreateAPIView(APIView):
#     """
#     List all papers for the authenticated user or create a new paper.
#     """
#     permission_classes = [IsAuthenticated]

#     def get(self, request, *args, **kwargs):
#         """Get all papers for the current user."""
#         user = request.user
#         papers = Paper.objects.filter(owner=user).order_by('-retrieved_date')

#         # Use the full PaperSerializer for detailed paper data
#         from .serializers import PaperSerializer
#         serializer = PaperSerializer(papers, many=True)
#         return Response(serializer.data)

#     def post(self, request, *args, **kwargs):
#         """Create a new paper."""
#         from .serializers import PaperSerializer
#         serializer = PaperSerializer(data=request.data)
#         if serializer.is_valid():
#             # Set the owner to the current user
#             serializer.save(owner=request.user)
#             return Response(serializer.data, status=201)
#         return Response(serializer.errors, status=400)


# class PaperDetailAPIView(APIView):
#     """
#     Retrieve, update or delete a paper instance.
#     """
#     permission_classes = [IsAuthenticated]

#     def get_object(self, pk, user):
#         """Get paper by ID, ensuring it belongs to the current user."""
#         try:
#             return Paper.objects.get(pk=pk, owner=user)
#         except Paper.DoesNotExist:
#             return None

#     def get(self, request, pk, *args, **kwargs):
#         """Get a specific paper."""
#         paper = self.get_object(pk, request.user)
#         if not paper:
#             return Response({'error': 'Paper not found'}, status=404)

#         from .serializers import PaperSerializer
#         serializer = PaperSerializer(paper)
#         return Response(serializer.data)

#     def put(self, request, pk, *args, **kwargs):
#         """Update a specific paper."""
#         paper = self.get_object(pk, request.user)
#         if not paper:
#             return Response({'error': 'Paper not found'}, status=404)

#         from .serializers import PaperSerializer
#         serializer = PaperSerializer(paper, data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data)
#         return Response(serializer.errors, status=400)

#     def delete(self, request, pk, *args, **kwargs):
#         """Delete a specific paper."""
#         paper = self.get_object(pk, request.user)
#         if not paper:
#             return Response({'error': 'Paper not found'}, status=404)

#         paper.delete()
#         return Response({'message': 'Paper deleted successfully'}, status=204)


# # Add this code to your backend/research/views.py file

# # ==============================================================================
# # PAPER & NOTE VIEWS (Personal Tools)
# # ==============================================================================

# class PaperViewSet(viewsets.ModelViewSet):
#     """
#     Handles a user's private library of collected papers.
#     Provides CRUD and searching capabilities.
#     """
#     serializer_class = PaperSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         """
#         Users can only see and manage their own papers.
#         Also includes full-text search functionality.
#         """
#         queryset = Paper.objects.filter(owner=self.request.user)
        
#         # Handle full-text search if a 'search' parameter is in the URL
#         search_term = self.request.query_params.get('search', None)
#         if search_term:
#             query = SearchQuery(search_term, search_type='websearch')
#             queryset = queryset.filter(search_vector=query)
            
#         return queryset

#     def perform_create(self, serializer):
#         """
#         Set the owner and the owner's primary research group automatically when a new
#         paper is added manually (e.g., via a form, not the fetcher).
#         """
#         user = self.request.user
#         # Find the user's primary, active membership
#         primary_membership = user.group_memberships.filter(status='ACTIVE', is_primary=True).first()
#         if not primary_membership:
#             raise ValidationError("You must be an active member of a primary research group to add papers.")
            
#         serializer.save(owner=user, research_group=primary_membership.research_group)

#     @action(detail=True, methods=['post'], url_path='toggle-favorite')
#     def toggle_favorite(self, request, pk=None):
#         """A custom action to toggle the 'is_favorite' status of a paper."""
#         paper = self.get_object() # get_object() correctly handles 404 and permission checks
#         paper.is_favorite = not paper.is_favorite
#         paper.save()
#         return Response(self.get_serializer(paper).data, status=status.HTTP_200_OK)


# class NoteViewSet(viewsets.ModelViewSet):
#     """
#     A nested API endpoint for a user's private notes on a specific paper.
#     Accessed via /api/papers/{paper_pk}/notes/
#     """
#     serializer_class = NoteSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         """
#         Users can only see their own notes for the specific paper in the URL.
#         """
#         return Note.objects.filter(
#             owner=self.request.user,
#             paper__pk=self.kwargs['paper_pk']
#         )

#     def perform_create(self, serializer):
#         """
#         When creating a note, validate that the user owns the parent paper.
#         """
#         paper = get_object_or_404(Paper, pk=self.kwargs['paper_pk'])
#         if paper.owner != self.request.user:
#             raise PermissionDenied("You can only create notes for papers in your own library.")
        
#         # Check if a note already exists for this user and paper
#         if Note.objects.filter(owner=self.request.user, paper=paper).exists():
#              raise ValidationError("A note for this paper already exists. Use a PATCH request to update it.")

#         serializer.save(owner=self.request.user, paper=paper)
        

# class ProjectListCreateAPIView(APIView):
#     """
#     List all projects for the authenticated user or create a new project.
#     """
#     permission_classes = [IsAuthenticated]

#     def get(self, request, *args, **kwargs):
#         """Get all projects where the user is a participant."""
#         user = request.user
#         projects = Project.objects.filter(
#             participations__person=user,
#             participations__is_active=True
#         ).order_by('-created_at')

#         serializer = DashboardProjectSerializer(projects, many=True)
#         return Response(serializer.data)

#     def post(self, request, *args, **kwargs):
#         """Create a new project."""
#         # Get the user's first active research group
#         user_membership = request.user.group_memberships.filter(status='ACTIVE').first()
#         if not user_membership:
#             return Response(
#                 {'error': 'You must be an active member of a research group to create projects'},
#                 status=400
#             )

#         project_data = {
#             'title': request.data.get('title', 'Untitled Project'),
#             'description': request.data.get('description', ''),
#             'content': request.data.get('content', ''),
#             'research_group': user_membership.research_group,
#             'status': 'IN_PROGRESS',
#         }

#         # Create the project (you'll need to add proper serializer validation)
#         project = Project.objects.create(**project_data)

#         # Add the current user as a participant
#         from .models import ProjectParticipation
#         ProjectParticipation.objects.create(
#             project=project,
#             person=request.user,
#             role_in_project='LEAD_RESEARCHER'
#         )

#         serializer = DashboardProjectSerializer(project)
#         return Response(serializer.data, status=201)


# class ProjectDetailAPIView(APIView):
#     """
#     Retrieve, update or delete a project instance.
#     """
#     permission_classes = [IsAuthenticated]

#     def get_object(self, pk, user):
#         """Get project by ID, ensuring the user is a participant."""
#         try:
#             return Project.objects.get(
#                 pk=pk,
#                 participations__person=user,
#                 participations__is_active=True
#             )
#         except Project.DoesNotExist:
#             return None

#     def get(self, request, pk, *args, **kwargs):
#         """Get a specific project."""
#         project = self.get_object(pk, request.user)
#         if not project:
#             return Response({'error': 'Project not found'}, status=404)

#         serializer = DashboardProjectSerializer(project)
#         return Response(serializer.data)

#     def patch(self, request, pk, *args, **kwargs):
#         """Update a specific project."""
#         project = self.get_object(pk, request.user)
#         if not project:
#             return Response({'error': 'Project not found'}, status=404)

#         # Update the content field if provided
#         if 'content' in request.data:
#             project.content = request.data['content']
#             project.save()

#         serializer = DashboardProjectSerializer(project)
#         return Response(serializer.data)

#     def delete(self, request, pk, *args, **kwargs):
#         """Delete a specific project."""
#         project = self.get_object(pk, request.user)
#         if not project:
#             return Response({'error': 'Project not found'}, status=404)

#         project.delete()
#         return Response({'message': 'Project deleted successfully'}, status=204)


# class ProjectMessagesAPIView(APIView):
#     """
#     Handle project messages.
#     """
#     permission_classes = [IsAuthenticated]

#     def get_project(self, pk, user):
#         """Get project by ID, ensuring the user is a participant."""
#         try:
#             return Project.objects.get(
#                 pk=pk,
#                 participations__person=user,
#                 participations__is_active=True
#             )
#         except Project.DoesNotExist:
#             return None

#     def get(self, request, pk, *args, **kwargs):
#         """Get all messages for a project."""
#         project = self.get_project(pk, request.user)
#         if not project:
#             return Response({'error': 'Project not found'}, status=404)

#         # For now, return mock messages. You can implement real message storage later
#         from django.utils import timezone
#         mock_messages = [
#             {
#                 "id": 1,
#                 "content": "Welcome to the project!",
#                 "author": "System",
#                 "timestamp": timezone.now().isoformat()
#             }
#         ]
#         return Response(mock_messages)

#     def post(self, request, pk, *args, **kwargs):
#         """Send a new message to a project."""
#         project = self.get_project(pk, request.user)
#         if not project:
#             return Response({'error': 'Project not found'}, status=404)

#         content = request.data.get('content', '')
#         if not content:
#             return Response({'error': 'Message content is required'}, status=400)

#         # For now, return a mock response. You can implement real message storage later
#         from django.utils import timezone
#         mock_message = {
#             "id": 2,
#             "content": content,
#             "author": f"{request.user.first_name} {request.user.last_name}",
#             "timestamp": timezone.now().isoformat()
#         }
#         return Response(mock_message, status=201)


# class ProjectMessageViewSet(viewsets.ModelViewSet):
#     """
#     API endpoint for sending and retrieving messages within a project's chat.
#     Accessed via /api/projects/{project_pk}/messages/
#     """
#     serializer_class = ProjectMessageSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_project(self):
#         """Helper method to get the parent project and check user participation."""
#         project_pk = self.kwargs['project_pk']
#         try:
#             # This single query gets the project AND verifies the user is a participant
#             project = Project.objects.get(
#                 pk=project_pk, 
#                 participations__person=self.request.user,
#                 participations__is_active=True
#             )
#             return project
#         except Project.DoesNotExist:
#             raise Http404("Project not found or you are not a participant.")

#     def get_queryset(self):
#         """
#         Returns the list of messages for the parent project, ordered by creation time.
#         """
#         project = self.get_project()
#         return project.messages.all().select_related('author').order_by('created_at')

#     def perform_create(self, serializer):
#         """
#         When a new message is created, automatically set the project and the author.
#         The get_project() method has already verified the user's permission.
#         """
#         project = self.get_project()
#         serializer.save(project=project, author=self.request.user)
        

# # ==============================================================================
# # PROJECT & COLLABORATION VIEWS
# # ==============================================================================

# class ProjectViewSet(viewsets.ModelViewSet):
#     """
#     API endpoint for creating, listing, and managing collaborative research projects.
#     """
#     serializer_class = ProjectSerializer
#     permission_classes = [permissions.IsAuthenticated] # Permissions are handled within methods

#     def get_queryset(self):
#         """
#         Users can see all projects where they are an active participant.
#         We use prefetch_related for a massive performance boost.
#         """
#         return Project.objects.filter(
#             participations__person=self.request.user,
#             participations__is_active=True
#         ).distinct().prefetch_related(
#             'participations__person',  # Get all participant user info in one query
#             'research_group__department' # Get group and department info
#         )

#     def perform_create(self, serializer):
#         """
#         When a new project is created, this method is called.
#         It validates permissions and adds the creator as the Lead Researcher.
#         """
#         user = self.request.user
#         research_group = serializer.validated_data.get('research_group')

#         # --- Validation ---
#         # A user must be an active member of the research group to create a project in it.
#         if not user.group_memberships.filter(research_group=research_group, status='ACTIVE').exists():
#             raise PermissionDenied("You can only create projects in a research group you are an active member of.")
        
#         # --- Save the Project ---
#         # The serializer.save() method will call the model's create() method.
#         project = serializer.save()
        
#         # --- CRITICAL STEP: Add the creator as a participant ---
#         # This solves the "Participants (0)" and permission denied errors.
#         ProjectParticipation.objects.create(
#             project=project,
#             person=user,
#             role_in_project=ProjectParticipation.ProjectRole.LEAD_RESEARCHER,
#             can_edit=True,
#             can_invite=True,
#             can_comment=True,
#             can_view=True
#         )


# class ProjectParticipationViewSet(viewsets.ModelViewSet):
#     """
#     A nested API endpoint for managing participants within a specific project.
#     Accessed via /api/projects/{project_pk}/participants/
#     """
#     serializer_class = ProjectParticipationSerializer
#     permission_classes = [permissions.IsAuthenticated]

#     def get_project(self):
#         """Helper method to get the parent project from the URL."""
#         project_pk = self.kwargs.get('project_pk')
#         try:
#             # Ensure the user is a participant of the project they are trying to access
#             project = Project.objects.get(
#                 pk=project_pk, 
#                 participations__person=self.request.user
#             )
#             return project
#         except Project.DoesNotExist:
#             raise Http404

#     def get_queryset(self):
#         """Returns the list of active participants for the parent project."""
#         project = self.get_project()
#         return project.participations.filter(is_active=True).select_related('person')

#     def perform_create(self, serializer):
#         """
#         Adds a new participant to the project.
#         This logic checks if the current user has permission to invite others.
#         """
#         project = self.get_project()
#         inviter_participation = project.participations.filter(person=self.request.user).first()

#         if not inviter_participation or not inviter_participation.can_invite:
#             raise PermissionDenied("You do not have permission to invite participants to this project.")

#         # The person to be added is passed in the request data
#         person_to_add = serializer.validated_data.get('person')

#         # Additional validation can be done here, e.g., ensure the new person
#         # is in the same research group.
        
#         serializer.save(project=project)




