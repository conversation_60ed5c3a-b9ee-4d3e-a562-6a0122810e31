import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  getMyResearchGroups,
  getGroupMembers,
  createGroupInvitation,
  getRoleTypes,
  getMyInvitations,
  startSupervisionProject,
  apiClient,
  debugAuth
} from "../services/api";
import { useAuth } from "../context/AuthContext";
import { Users, Mail, RefreshCw, Link, Copy, Briefcase } from "lucide-react"; // Import icons
import WorkspaceComponent from "../components/WorkspaceComponent";


const StatCard = ({ icon, label, value, change }) => (
  <div style={styles.statCard}>
    <div style={{ ...styles.statIcon, ...styles[icon.color] }}>{icon.char}</div>
    <div>
      <h3 style={styles.statLabel}>{label}</h3>
      <p style={styles.statNumber}>{value}</p>
      {change && <p style={{ ...styles.statChange, color: change.color }}>{change.text}</p>}
    </div>
  </div>
);

const MembersTab = ({ members, onStartSupervision }) => (
  <div>
    <h2>Member Roster</h2>
    <table style={styles.table}>
      <thead>
        <tr>
          <th style={styles.th}>Name</th>
          <th style={styles.th}>Role</th>
          <th style={styles.th}>Actions</th>
        </tr>
      </thead>
      <tbody>
        {members.length > 0 ? (
          members.map((member) => (
            <tr key={member.id}>
              <td style={styles.td}>{`${member.first_name} ${member.last_name}`}</td>
              <td style={styles.td}>{member.primary_role?.name || "N/A"}</td>
              <td style={styles.td}>
                {member.primary_role?.name === "Student" && (
                  <button onClick={() => onStartSupervision(member.id, member.username)} style={styles.button}>
                    Start Supervision
                  </button>
                )}
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan="3" style={styles.emptyState}>
              No members found.
            </td>
          </tr>
        )}
      </tbody>
    </table>
  </div>
);

const AdminDashboard = () => {
  const { user, userToken } = useAuth();
  const navigate = useNavigate();

  // Data State
  const [researchGroup, setResearchGroup] = useState(null);
  const [members, setMembers] = useState([]);
  const [roleTypes, setRoleTypes] = useState([]);
  const [invitations, setInvitations] = useState([]);

  const [stats, setStats] = useState({ total_students: 0, active_projects: 0 }); // Add stats state

  // UI State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState("workspace");

  // Form State
  const [inviteEmail, setInviteEmail] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [inviteStatus, setInviteStatus] = useState({ type: "", message: "" });

  const fetchData = async () => {
    setLoading(true);
    setError("");
    try {
      const groupsResponse = await getMyResearchGroups();

      if (groupsResponse.data && groupsResponse.data.results && groupsResponse.data.results.length > 0) {
        const firstGroup = groupsResponse.data.results[0];
        setResearchGroup(firstGroup);

        try {
          // Call them separately to see which one fails
          let membersResponse, rolesResponse;

          try {
            membersResponse = await getGroupMembers();
            setMembers(membersResponse.data || []);
          } catch (membersErr) {
            setError(`Failed to load members: ${membersErr.message}`);
          }

          try {
            rolesResponse = await getRoleTypes();

            // Handle both paginated and non-paginated responses
            let rolesArray = [];
            if (Array.isArray(rolesResponse.data)) {
              rolesArray = rolesResponse.data;
            } else if (rolesResponse.data && rolesResponse.data.results) {
              rolesArray = rolesResponse.data.results;
            }

            setRoleTypes(rolesArray);

            // Set a default role for the invite form
            if (rolesArray && rolesArray.length > 0) {
              const studentRole = rolesArray.find((r) => r.name.toLowerCase() === "student");
              if (studentRole) {
                setSelectedRole(studentRole.id);
              }
            }
          } catch (rolesErr) {
            setError(`Failed to load roles: ${rolesErr.message}`);
          }

          try {
            const invitationsResponse = await getMyInvitations();

            // Handle both paginated and non-paginated responses
            let invitationsArray = [];
            if (Array.isArray(invitationsResponse.data)) {
              invitationsArray = invitationsResponse.data;
            } else if (invitationsResponse.data && invitationsResponse.data.results) {
              invitationsArray = invitationsResponse.data.results;
            }

            setInvitations(invitationsArray);
          } catch {
            // Don't set error for invitations failure, just log it
          }
        } catch (secondaryErr) {
          setError(`Unexpected error: ${secondaryErr.message}`);
        }
      } else {
        setError("You are not an active member of any research groups.");
      }
    } catch (err) {
      setError(err.response?.data?.detail || err.message || "Could not load your workspace data.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on initial component load
  useEffect(() => {
    fetchData();
  }, []);

  const copyInvitationLink = (invitationCode) => {
    const invitationUrl = `${window.location.origin}/register?code=${invitationCode}`;
    navigator.clipboard
      .writeText(invitationUrl)
      .then(() => {
        alert("Invitation link copied to clipboard!");
      })
      .catch(() => {
        alert("Failed to copy link. Please copy manually: " + invitationUrl);
      });
  };

  const handleInviteSubmit = async (e) => {
    e.preventDefault();
    if (!researchGroup?.id || !selectedRole) {
      setInviteStatus({ type: "error", message: "Error: A group and role must be selected." });
      return;
    }

    setInviteStatus({ type: "loading", message: "Sending..." });
    try {
      const response = await createGroupInvitation(researchGroup.id, inviteEmail, selectedRole);
      setInviteStatus({ type: "success", message: `Invitation successfully created for ${inviteEmail}!` });
      setInviteEmail(""); // Clear input on success

      // Refresh the invitations list
      try {
        const invitationsResponse = await getMyInvitations();
        let invitationsArray = [];
        if (Array.isArray(invitationsResponse.data)) {
          invitationsArray = invitationsResponse.data;
        } else if (invitationsResponse.data && invitationsResponse.data.results) {
          invitationsArray = invitationsResponse.data.results;
        }
        setInvitations(invitationsArray);
      } catch {
        // Don't show error to user for refresh failure
      }
    } catch (error) {
      // Extract detailed error message
      let errorMessage = "Failed to create invitation.";
      if (error.response?.data) {
        if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.email) {
          errorMessage = `Email error: ${error.response.data.email[0]}`;
        } else if (error.response.data.intended_role) {
          errorMessage = `Role error: ${error.response.data.intended_role[0]}`;
        } else if (error.response.data.research_group) {
          errorMessage = `Group error: ${error.response.data.research_group[0]}`;
        } else if (error.response.data.non_field_errors) {
          errorMessage = error.response.data.non_field_errors[0];
        } else {
          errorMessage = JSON.stringify(error.response.data);
        }
      }

      setInviteStatus({ type: "error", message: `Error: ${errorMessage}` });
    }
  };

  const handleDebugAuth = async () => {
    try {
      console.log("🔍 DEBUG: Testing authentication...");
      console.log("🔍 DEBUG: API Client headers:", apiClient.defaults.headers.common);

      const response = await debugAuth();
      console.log("🔍 DEBUG: Auth debug response:", response.data);
      alert(`Authentication Status: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error) {
      console.error("🔍 DEBUG: Auth debug error:", error);
      alert(`Authentication Error: ${error.response?.data?.detail || error.message}`);
    }
  };

  const handleStartSupervision = async (studentId, studentName) => {
    if (window.confirm(`Are you sure you want to start a new supervision project for ${studentName}?`)) {
      try {
        console.log("🔍 DEBUG: Starting supervision for student:", studentId);
        console.log("🔍 DEBUG: API Client headers:", apiClient.defaults.headers.common);

        // Call the new API endpoint
        const response = await startSupervisionProject(studentId);
        const newProject = response.data;

        console.log("🔍 DEBUG: Supervision project created:", newProject);

        // On success, redirect the user to the new project's detail page
        alert(`Supervision project '${newProject.title}' created successfully!`);
        navigate(`/projects/${newProject.id}`); // <-- THE REDIRECT
      } catch (error) {
        console.error("🔍 DEBUG: Supervision creation error:", error);
        console.error("🔍 DEBUG: Error response:", error.response);
        console.error("🔍 DEBUG: Error response data:", error.response?.data);
        console.error("🔍 DEBUG: Error status:", error.response?.status);

        let errorMessage = "Could not start the supervision project.";

        if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.response?.data?.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response?.status === 401) {
          errorMessage = "Authentication failed. Please log in again.";
        } else if (error.response?.status === 403) {
          errorMessage = "You don't have permission to create supervision projects.";
        } else if (error.message) {
          errorMessage = error.message;
        }

        alert(`Error: ${errorMessage}`);
      }
    }
  };

  if (loading) {
    return (
      <div style={{ ...styles.card, margin: "20px auto", maxWidth: "1200px", textAlign: "center" }}>
        Loading Administrator Dashboard...
      </div>
    );
  }

  return (
    <div style={styles.container}>
      <div style={{ ...styles.card, ...styles.header }}>
        <div>
          <h1 style={styles.title}>Administrator Dashboard</h1>
          <h2 style={styles.subtitle}>{researchGroup?.name || "No Group Found"}</h2>
        </div>
        <div style={{ display: "flex", gap: "12px" }}>
          <button onClick={handleDebugAuth} title="Debug Authentication" style={styles.buttonSecondary}>
            🔍 Debug Auth
          </button>
          <button onClick={fetchData} title="Refresh Data" style={styles.buttonSecondary}>
            <RefreshCw size={14} />
          </button>
        </div>
      </div>

      {error && <p style={{ ...styles.card, color: "red" }}>{error}</p>}

      {/* Statistics Cards Section */}
      <div style={styles.statsGrid}>
        <StatCard
          icon={{ char: "👥", color: "blue" }}
          label="Total Members"
          value={members.length}
          change={{ text: `${members.filter(m => m.primary_role_name === 'Student').length} students`, color: "#059669" }}
        />
        <StatCard
          icon={{ char: "📊", color: "green" }}
          label="Active Projects"
          value={stats.active_projects || 0}
          change={{ text: "Research projects", color: "#059669" }}
        />
        <StatCard
          icon={{ char: "📧", color: "purple" }}
          label="Pending Invitations"
          value={invitations.length}
          change={{ text: "Awaiting response", color: "#dc2626" }}
        />
        <StatCard
          icon={{ char: "🎓", color: "orange" }}
          label="Students"
          value={stats.total_students || members.filter(m => m.primary_role_name === 'Student').length}
          change={{ text: "Under supervision", color: "#059669" }}
        />
      </div>

      <div style={styles.tabs}>
        <button
          onClick={() => setActiveTab("workspace")}
          style={activeTab === "workspace" ? styles.tabActive : styles.tab}
        >
          <Briefcase size={16} /> Workspace
        </button>
        <button onClick={() => setActiveTab("members")} style={activeTab === "members" ? styles.tabActive : styles.tab}>
          <Users size={16} /> Members ({members.length})
        </button>
        <button
          onClick={() => setActiveTab("invitations")}
          style={activeTab === "invitations" ? styles.tabActive : styles.tab}
        >
          <Link size={16} /> Invitations ({invitations.length})
        </button>
        <button onClick={() => setActiveTab("invite")} style={activeTab === "invite" ? styles.tabActive : styles.tab}>
          <Mail size={16} /> Invite Members
        </button>
      </div>

      {activeTab === "workspace" ? (
        <WorkspaceComponent />
      ) : (
        <div style={styles.card}>
          {activeTab === "members" && (
            <div>
              <h2>Member Roster</h2>
              <table style={styles.table}>
                <thead>
                  <tr>
                    <th style={styles.th}>Name</th>
                    <th style={styles.th}>Username</th>
                    <th style={styles.th}>Email</th>
                    <th style={styles.th}>Primary Role</th>
                    <th style={styles.th}>Actions</th>
                  </tr>
                </thead>

                <tbody>
                  {members.length > 0 ? (
                    members.map((member) => (
                      <tr key={member.id}>
                        {/* Data columns remain the same */}
                        <td style={styles.td}>{`${member.first_name} ${member.last_name}`}</td>
                        <td style={styles.td}>{member.username}</td>
                        <td style={styles.td}>{member.email}</td>
                        <td style={styles.td}>{member.primary_role_name || "N/A"}</td>

                        {/* --- ADD THIS NEW ACTIONS COLUMN --- */}
                        <td style={styles.td}>
                          {/* 
                          This is the conditional rendering logic.
                          The button will ONLY be rendered if the member's primary role is 'Student'.
                          For Professors or other roles, this cell will be empty.
                        */}
                          {member.primary_role_name === "Student" && (
                            <button
                              onClick={() =>
                                handleStartSupervision(member.id, `${member.first_name} ${member.last_name}`.trim())
                              }
                              style={styles.button} // Use a consistent button style
                            >
                              Start Supervision
                            </button>
                          )}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="5" style={{ textAlign: "center", padding: "20px" }}>
                        There are no members in this research group yet.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
          {activeTab === "invitations" && (
            <div>
              <h2>Pending Invitations</h2>
              <p style={styles.description}>
                These are invitations that have been sent but not yet accepted. Click "Copy Link" to get the
                registration URL.
              </p>
              {invitations.length === 0 ? (
                <p style={{ textAlign: "center", color: "#64748b", marginTop: "40px" }}>
                  No pending invitations found.
                </p>
              ) : (
                <table style={styles.table}>
                  <thead>
                    <tr>
                      <th style={styles.th}>Email</th>
                      <th style={styles.th}>Role</th>
                      <th style={styles.th}>Status</th>
                      <th style={styles.th}>Created</th>
                      <th style={styles.th}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invitations.map((invitation) => (
                      <tr key={invitation.id}>
                        <td style={styles.td}>{invitation.invitee_email}</td>
                        <td style={styles.td}>{invitation.intended_role?.name || "N/A"}</td>
                        <td style={styles.td}>
                          <span
                            style={{
                              ...styles.statusBadge,
                              backgroundColor: invitation.status === "PENDING" ? "#fef3c7" : "#fee2e2",
                              color: invitation.status === "PENDING" ? "#92400e" : "#991b1b",
                            }}
                          >
                            {invitation.status}
                          </span>
                        </td>
                        <td style={styles.td}>{new Date(invitation.created_at).toLocaleDateString()}</td>
                        <td style={styles.td}>
                          {invitation.status === "PENDING" && (
                            <button
                              onClick={() => copyInvitationLink(invitation.code)}
                              style={styles.buttonSecondary}
                              title="Copy registration link"
                            >
                              <Copy size={14} /> Copy Link
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          )}
          {activeTab === "invite" && (
            <div>
              <h2>Invite a New Member</h2>
              <p style={styles.description}>
                An invitation with a unique registration code will be created. You must send the code to the user.
              </p>
              <form onSubmit={handleInviteSubmit} style={{ marginTop: "20px", maxWidth: "600px" }}>
                <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                  <input
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    style={styles.input}
                  />
                  <select value={selectedRole} onChange={(e) => setSelectedRole(e.target.value)} style={styles.input}>
                    <option value="">Select Role...</option>
                    {Array.isArray(roleTypes)
                      ? roleTypes.map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.name}
                          </option>
                        ))
                      : null}
                  </select>
                  <button type="submit" style={styles.button} disabled={inviteStatus.type === "loading"}>
                    {inviteStatus.type === "loading" ? "Creating..." : "Create Invitation"}
                  </button>
                </div>
              </form>
              {inviteStatus.message && (
                <p style={{ ...styles.statusMessage, color: inviteStatus.type === "error" ? "#dc2626" : "#059669" }}>
                  {inviteStatus.message}
                </p>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// --- STYLES ---
const styles = {
  container: { maxWidth: "1200px", margin: "20px auto", fontFamily: "system-ui, sans-serif" },
  card: { background: "white", padding: "25px", borderRadius: "12px", boxShadow: "0 4px 20px rgba(0,0,0,0.05)" },
  header: { display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" },
  title: { margin: 0, fontSize: "1.8rem" },
  subtitle: { margin: "5px 0 0 0", color: "#64748b", fontWeight: "normal" },

  // Statistics Cards Styles
  statsGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
    gap: "20px",
    marginBottom: "30px"
  },
  statCard: {
    display: "flex",
    alignItems: "center",
    gap: "16px",
    padding: "24px",
    background: "white",
    borderRadius: "12px",
    boxShadow: "0 4px 20px rgba(0,0,0,0.05)"
  },
  statIcon: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "48px",
    height: "48px",
    borderRadius: "8px",
    fontSize: "24px"
  },
  statLabel: {
    margin: "0 0 4px 0",
    fontSize: "0.875rem",
    color: "#64748b",
    fontWeight: "500"
  },
  statNumber: {
    margin: "0 0 4px 0",
    fontSize: "2rem",
    fontWeight: "700",
    color: "#1e293b"
  },
  statChange: {
    margin: 0,
    fontSize: "0.75rem",
    fontWeight: "500"
  },
  blue: { backgroundColor: "#eff6ff", color: "#3b82f6" },
  green: { backgroundColor: "#f0fdf4", color: "#22c55e" },
  purple: { backgroundColor: "#faf5ff", color: "#a855f7" },
  orange: { backgroundColor: "#fff7ed", color: "#f97316" },
  tabs: { display: "flex", gap: "4px", marginBottom: "20px" },
  tab: {
    padding: "10px 20px",
    border: "none",
    background: "#f1f5f9",
    cursor: "pointer",
    color: "#475569",
    borderRadius: "8px 8px 0 0",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    fontWeight: "500",
    fontSize: "14px",
  },
  tabActive: {
    padding: "10px 20px",
    border: "none",
    background: "white",
    cursor: "pointer",
    color: "#0f172a",
    borderRadius: "8px 8px 0 0",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    fontWeight: "600",
    boxShadow: "0 -2px 5px rgba(0,0,0,0.03)",
  },
  description: { color: "#64748b", marginTop: "-10px", marginBottom: "20px" },
  table: { width: "100%", borderCollapse: "collapse", marginTop: "15px" },
  th: {
    textAlign: "left",
    padding: "12px",
    borderBottom: "2px solid #f1f5f9",
    background: "#f8fafc",
    color: "#64748b",
    textTransform: "uppercase",
    fontSize: "0.8rem",
  },
  td: { padding: "12px", borderBottom: "1px solid #f1f5f9" },
  input: { flex: 1, padding: "10px", borderRadius: "6px", border: "1px solid #cbd5e1" },
  button: {
    background: "#3b82f6",
    color: "white",
    border: "none",
    padding: "10px 15px",
    borderRadius: "6px",
    cursor: "pointer",
    fontWeight: "bold",
  },
  buttonSecondary: {
    background: "white",
    color: "#334155",
    border: "1px solid #cbd5e1",
    padding: "8px",
    borderRadius: "6px",
    cursor: "pointer",
  },
  statusMessage: { marginTop: "15px", padding: "10px", borderRadius: "6px", border: "1px solid transparent" },
  statusBadge: {
    padding: "4px 8px",
    borderRadius: "4px",
    fontSize: "0.75rem",
    fontWeight: "500",
    textTransform: "uppercase"
  },
};

export default AdminDashboard;


