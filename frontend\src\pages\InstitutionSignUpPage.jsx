import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { registerInstitution } from "../services/api";
import { useAuth } from "../context/AuthContext";
import { Eye, EyeOff, Building2, User, Mail, Lock, Loader2 } from "lucide-react";
import "./InstitutionSignUpPage.css";

const InstitutionSignUpPage = () => {
  const [formData, setFormData] = useState({
    institution_name: "",
    admin_first_name: "",
    admin_last_name: "",
    admin_username: "",
    admin_email: "",
    password: "",
    confirm_password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Institution validation
    if (!formData.institution_name.trim()) {
      newErrors.institution_name = "Institution name is required";
    } else if (formData.institution_name.length < 3) {
      newErrors.institution_name = "Institution name must be at least 3 characters";
    }

    // Name validation
    if (!formData.admin_first_name.trim()) {
      newErrors.admin_first_name = "First name is required";
    }
    if (!formData.admin_last_name.trim()) {
      newErrors.admin_last_name = "Last name is required";
    }

    // Username validation
    if (!formData.admin_username.trim()) {
      newErrors.admin_username = "Username is required";
    } else if (formData.admin_username.length < 3) {
      newErrors.admin_username = "Username must be at least 3 characters";
    }

    // Email validation
    if (!formData.admin_email.trim()) {
      newErrors.admin_email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.admin_email)) {
      newErrors.admin_email = "Please enter a valid email address";
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = "Password must contain uppercase, lowercase, and numbers";
    }

    // Confirm password validation
    if (formData.password !== formData.confirm_password) {
      newErrors.confirm_password = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      // Step 1: Create the institution and the admin user
      await registerInstitution(formData);

      // Step 2: Automatically log the new admin in
      await login(formData.admin_username, formData.password);

      // Step 3: Redirect to the dashboard
      navigate("/dashboard", {
        state: {
          message: "Institution account created successfully!",
          type: "success",
        },
      });
    } catch (err) {
      console.error("Registration error:", err);

      // Handle backend validation errors
      const errorData = err.response?.data;
      if (errorData) {
        // Map backend errors to field-specific errors
        const fieldErrors = {};
        Object.keys(errorData).forEach((key) => {
          if (Array.isArray(errorData[key])) {
            fieldErrors[key] = errorData[key].join(" ");
          } else {
            fieldErrors[key] = errorData[key];
          }
        });
        setErrors(fieldErrors);
      } else {
        setErrors({ general: "Registration failed. Please try again." });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="institution-signup-container">
      <div className="institution-signup-card">
        <div className="signup-header">
          <Building2 size={48} className="institution-icon" />
          <h1>Set Up Your Institution</h1>
          <p>Create your institution's account and your personal administrator profile</p>
        </div>

        <form onSubmit={handleSubmit} className="signup-form">
          {/* Institution Information */}
          <div className="form-section">
            <h3>Institution Information</h3>
            <div className="input-group">
              <label htmlFor="institution_name">
                <Building2 size={18} />
                Institution Name *
              </label>
              <input
                id="institution_name"
                name="institution_name"
                type="text"
                value={formData.institution_name}
                onChange={handleChange}
                placeholder="Enter institution name"
                className={errors.institution_name ? "error" : ""}
                disabled={loading}
              />
              {errors.institution_name && <span className="error-text">{errors.institution_name}</span>}
            </div>
          </div>

          {/* Admin Information */}
          <div className="form-section">
            <h3>Administrator Profile</h3>
            <div className="name-row">
              <div className="input-group">
                <label htmlFor="admin_first_name">
                  <User size={18} />
                  First Name *
                </label>
                <input
                  id="admin_first_name"
                  name="admin_first_name"
                  type="text"
                  value={formData.admin_first_name}
                  onChange={handleChange}
                  placeholder="First name"
                  className={errors.admin_first_name ? "error" : ""}
                  disabled={loading}
                />
                {errors.admin_first_name && <span className="error-text">{errors.admin_first_name}</span>}
              </div>
              <div className="input-group">
                <label htmlFor="admin_last_name">
                  <User size={18} />
                  Last Name *
                </label>
                <input
                  id="admin_last_name"
                  name="admin_last_name"
                  type="text"
                  value={formData.admin_last_name}
                  onChange={handleChange}
                  placeholder="Last name"
                  className={errors.admin_last_name ? "error" : ""}
                  disabled={loading}
                />
                {errors.admin_last_name && <span className="error-text">{errors.admin_last_name}</span>}
              </div>
            </div>

            <div className="input-group">
              <label htmlFor="admin_username">
                <User size={18} />
                Username *
              </label>
              <input
                id="admin_username"
                name="admin_username"
                type="text"
                value={formData.admin_username}
                onChange={handleChange}
                placeholder="Choose a username"
                className={errors.admin_username ? "error" : ""}
                disabled={loading}
              />
              {errors.admin_username && <span className="error-text">{errors.admin_username}</span>}
            </div>

            <div className="input-group">
              <label htmlFor="admin_email">
                <Mail size={18} />
                Email Address *
              </label>
              <input
                id="admin_email"
                name="admin_email"
                type="email"
                value={formData.admin_email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                className={errors.admin_email ? "error" : ""}
                disabled={loading}
              />
              {errors.admin_email && <span className="error-text">{errors.admin_email}</span>}
            </div>
          </div>

          {/* Password Section */}
          <div className="form-section">
            <h3>Security</h3>
            <div className="input-group">
              <label htmlFor="password">
                <Lock size={18} />
                Password *
              </label>
              <div className="password-input-container">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Create a strong password"
                  className={errors.password ? "error" : ""}
                  disabled={loading}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              {errors.password && <span className="error-text">{errors.password}</span>}
            </div>

            <div className="input-group">
              <label htmlFor="confirm_password">
                <Lock size={18} />
                Confirm Password *
              </label>
              <div className="password-input-container">
                <input
                  id="confirm_password"
                  name="confirm_password"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirm_password}
                  onChange={handleChange}
                  placeholder="Confirm your password"
                  className={errors.confirm_password ? "error" : ""}
                  disabled={loading}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={loading}
                >
                  {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              {errors.confirm_password && <span className="error-text">{errors.confirm_password}</span>}
            </div>
          </div>

          {errors.general && <div className="error-message">{errors.general}</div>}

          <button type="submit" disabled={loading} className="submit-button">
            {loading ? (
              <>
                <Loader2 size={20} className="spinner" />
                Creating Account...
              </>
            ) : (
              "Create Institution Account"
            )}
          </button>

          <div className="login-link">
            <p>
              Already have an account? <Link to="/login">Log In</Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InstitutionSignUpPage;



