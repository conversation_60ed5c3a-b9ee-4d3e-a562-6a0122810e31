#!/bin/sh

echo "Entrypoint: Waiting for PostgreSQL to be available..."

# Use the environment variables to wait for the database
while ! nc -z $DB_HOST $DB_PORT; do
  sleep 0.1
done

echo "Entrypoint: PostgreSQL is available."
echo "Entrypoint: Applying database migrations..."

# Apply database migrations
python manage.py migrate --noinput

echo "Entrypoint: Migrations complete. Executing main command..."

# Execute the main command passed to the container
exec "$@"





