.institution-signup-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.institution-signup-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 600px;
}

.signup-header {
    text-align: center;
    margin-bottom: 32px;
}

.signup-header h1 {
    color: #2d3748;
    font-size: 2rem;
    font-weight: 700;
    margin: 16px 0 8px 0;
}

.signup-header p {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.institution-icon {
    color: #4c51bf;
    background: #f0fff4;
    padding: 12px;
    border-radius: 50%;
}

.signup-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-section {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
}

.form-section h3 {
    color: #2d3748;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 20px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #edf2f7;
}

.name-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.input-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.875rem;
}

.input-group input {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    width: 100%;
    box-sizing: border-box;
}

.input-group input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.input-group input.error {
    border-color: #e53e3e;
}

.input-group input:disabled {
    background-color: #f7fafc;
    cursor: not-allowed;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.password-toggle:hover:not(:disabled) {
    color: #2d3748;
}

.password-toggle:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.error-text {
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 4px;
}

.error-message {
    background-color: #fed7d7;
    color: #c53030;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #feb2b2;
    text-align: center;
}

.submit-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 16px;
}

.submit-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.submit-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.login-link {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e2e8f0;
}

.login-link p {
    color: #718096;
    margin: 0;
}

.login-link a {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
}

.login-link a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .institution-signup-card {
        padding: 24px;
        margin: 20px;
    }

    .name-row {
        grid-template-columns: 1fr;
    }

    .form-section {
        padding: 20px;
    }

    .signup-header h1 {
        font-size: 1.75rem;
    }
}

@media (max-width: 480px) {
    .institution-signup-container {
        padding: 10px;
    }

    .institution-signup-card {
        padding: 20px;
    }

    .signup-header h1 {
        font-size: 1.5rem;
    }
}


