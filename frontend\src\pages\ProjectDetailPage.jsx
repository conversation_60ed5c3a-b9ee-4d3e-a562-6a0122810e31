import React, { useState, useEffect, useRef, useCallback } from "react";
import { useParams } from "react-router-dom";
import {
  getProjectDetail,
  sendProjectMessage,
  getProjectMessages,
  updateProjectContent,
  clearProjectCache,
} from "../services/api";
import TipTapEditor from "../components/TiptapEditor";
import {
  Loader2,
  Users,
  MessageSquare,
  Send,
  RefreshCw,
  AlertCircle,
  Clock,
  MoreVertical,
  Edit3,
  Save,
} from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { formatDistanceToNow } from "date-fns";

const ProjectDetailPage = () => {
  const { projectId } = useParams();
  const { user } = useAuth();
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState("");
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);
  const [savingContent, setSavingContent] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [editorContent, setEditorContent] = useState("");
  const [activeTab, setActiveTab] = useState("editor"); // 'editor' or 'chat'

  const messagesEndRef = useRef(null);
  const saveTimeoutRef = useRef(null);
  const chatContainerRef = useRef(null);

  // Scroll to bottom of chat
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Debounced save function for editor content
  const debouncedSave = useCallback(
    (content) => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      saveTimeoutRef.current = setTimeout(async () => {
        setSavingContent(true);
        try {
          await updateProjectContent(projectId, content);
          setLastSaved(new Date());
        } catch (err) {
          console.error("Failed to save content:", err);
          setError("Failed to save changes");
        } finally {
          setSavingContent(false);
        }
      }, 2000); // 2 second debounce
    },
    [projectId]
  );

  const handleEditorChange = (newContent) => {
    setEditorContent(newContent);
    debouncedSave(newContent);
  };

  const fetchProjectData = async (isRefresh = false) => {
    try {
      if (isRefresh) setRefreshing(true);
      else setLoading(true);

      setError("");
      clearProjectCache(projectId); // Clear cache on refresh

      const [projectRes, messagesRes] = await Promise.allSettled([
        getProjectDetail(projectId),
        getProjectMessages(projectId),
      ]);

      if (projectRes.status === "fulfilled") {
        console.log("Project data received:", projectRes.value.data);
        setProject(projectRes.value.data);
        setEditorContent(projectRes.value.data.content || "");
      } else {
        console.error("Failed to load project:", projectRes.reason);
        throw new Error("Failed to load project");
      }

      if (messagesRes.status === "fulfilled") {
        console.log("Messages data received:", messagesRes.value.data);
        // Handle both array and object with 'value' property
        const messagesData = messagesRes.value.data;
        if (Array.isArray(messagesData)) {
          setMessages(messagesData);
        } else if (messagesData && Array.isArray(messagesData.value)) {
          setMessages(messagesData.value);
        } else {
          setMessages([]);
        }
      }
    } catch (err) {
      console.error("Failed to fetch project data:", err);
      setError(err.message || "Could not load the project.");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchProjectData();
  }, [projectId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle "new" project case - show creation message
  if (projectId === "new") {
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <h2>Create New Project</h2>
        <p>Project creation is not yet implemented. Please go back to the projects list.</p>
        <button
          onClick={() => window.history.back()}
          style={{
            padding: "12px 24px",
            backgroundColor: "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            marginTop: "16px"
          }}
        >
          Go Back
        </button>
      </div>
    );
  }

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || sendingMessage) return;

    setSendingMessage(true);
    try {
      // Optimistic update
      const tempMessage = {
        id: `temp-${Date.now()}`,
        content: newMessage,
        user: user,
        created_at: new Date().toISOString(),
        isSending: true,
      };

      setMessages((prev) => [...prev, tempMessage]);
      setNewMessage("");

      // Real API call
      const response = await sendProjectMessage(projectId, { content: newMessage });

      // Replace temporary message with real one
      setMessages((prev) => prev.map((msg) => (msg.id === tempMessage.id ? response : msg)));
    } catch (err) {
      console.error("Failed to send message:", err);
      setError("Failed to send message. Please try again.");

      // Remove the optimistic message on error
      setMessages((prev) => prev.filter((msg) => !msg.isSending));
    } finally {
      setSendingMessage(false);
    }
  };

  const handleRefresh = () => {
    fetchProjectData(true);
  };

  const isUserParticipant = project?.participations?.some((p) => p.person.id === user?.id);

  if (loading) {
    return (
      <div style={styles.loadingContainer}>
        <Loader2 size={32} style={styles.spinner} />
        <p>Loading project details...</p>
      </div>
    );
  }

  if (error && !project) {
    return (
      <div style={styles.errorContainer}>
        <AlertCircle size={24} />
        <h3>Unable to load project</h3>
        <p>{error}</p>
        <button onClick={handleRefresh} style={styles.retryButton}>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div style={styles.pageContainer}>
      {/* Header */}
      <header style={styles.header}>
        <div style={styles.headerContent}>
          <div>
            <h1 style={styles.projectTitle}>{project?.title}</h1>
            <div style={styles.headerMeta}>
              <span
                style={{
                  ...styles.statusBadge,
                  ...styles[`status-${project?.status ? String(project.status).toLowerCase() : 'unknown'}`],
                }}
              >
                {project?.status || 'Unknown'}
              </span>
              {lastSaved && (
                <span style={styles.lastSaved}>
                  <Clock size={14} />
                  Saved {lastSaved ? formatDistanceToNow(lastSaved, { addSuffix: true }) : 'recently'}
                </span>
              )}
              {savingContent && (
                <span style={styles.savingIndicator}>
                  <Loader2 size={14} style={styles.spinning} />
                  Saving...
                </span>
              )}
            </div>
          </div>
          <div style={styles.headerActions}>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              style={styles.refreshButton}
              title="Refresh project data"
            >
              <RefreshCw size={16} style={refreshing ? styles.spinning : {}} />
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div style={styles.contentContainer}>
        {/* Editor/Chat Tabs */}
        <div style={styles.tabContainer}>
          <button
            style={{
              ...styles.tab,
              ...(activeTab === "editor" && styles.tabActive),
            }}
            onClick={() => setActiveTab("editor")}
          >
            <Edit3 size={16} />
            Editor
          </button>
          <button
            style={{
              ...styles.tab,
              ...(activeTab === "chat" && styles.tabActive),
            }}
            onClick={() => setActiveTab("chat")}
          >
            <MessageSquare size={16} />
            Chat ({messages.length})
          </button>
        </div>

        {/* Main Content Area */}
        <main style={styles.mainContent}>
          {activeTab === "editor" ? (
            <div style={styles.editorSection}>
              <TipTapEditor
                content={editorContent}
                onChange={handleEditorChange}
                editable={isUserParticipant}
                placeholder={
                  isUserParticipant
                    ? "Start writing your project content..."
                    : "You don't have permission to edit this project"
                }
              />
            </div>
          ) : (
            <div style={styles.chatSection}>
              <div ref={chatContainerRef} style={styles.chatMessages}>
                {!Array.isArray(messages) || messages.length === 0 ? (
                  <div style={styles.emptyChat}>
                    <MessageSquare size={48} />
                    <p>No messages yet</p>
                    <p style={styles.emptyChatSubtitle}>Start the conversation!</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      style={{
                        ...styles.message,
                        ...(message.user?.id === user?.id && styles.messageOwn),
                      }}
                    >
                      <div style={styles.messageHeader}>
                        <strong style={styles.messageAuthor}>
                          {message.author || `${message.user?.first_name || ''} ${message.user?.last_name || ''}`.trim() || 'Unknown User'}
                        </strong>
                        <span style={styles.messageTime}>
                          {(() => {
                            try {
                              const timestamp = message.timestamp || message.created_at;
                              if (!timestamp) return 'Unknown time';
                              const date = new Date(timestamp);
                              return isNaN(date.getTime()) ? 'Unknown time' : formatDistanceToNow(date, { addSuffix: true });
                            } catch (error) {
                              return 'Unknown time';
                            }
                          })()}
                        </span>
                      </div>
                      <p style={styles.messageContent}>{message.content}</p>
                      {message.isSending && (
                        <div style={styles.sendingIndicator}>
                          <Loader2 size={12} style={styles.spinning} />
                          Sending...
                        </div>
                      )}
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>

              {isUserParticipant ? (
                <form onSubmit={handleSendMessage} style={styles.chatForm}>
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type a message..."
                    disabled={sendingMessage}
                    style={styles.chatInput}
                  />
                  <button type="submit" disabled={!newMessage.trim() || sendingMessage} style={styles.chatButton}>
                    {sendingMessage ? <Loader2 size={16} style={styles.spinning} /> : <Send size={16} />}
                  </button>
                </form>
              ) : (
                <div style={styles.chatDisabled}>
                  <AlertCircle size={16} />
                  You need to be a participant to send messages
                </div>
              )}
            </div>
          )}
        </main>

        {/* Sidebar */}
        <aside style={styles.sidebar}>
          <section style={styles.sidebarSection}>
            <h3 style={styles.sidebarTitle}>
              <Users size={18} />
              Participants ({project?.participations?.length || 0})
            </h3>
            <div style={styles.participantList}>
              {project?.participations?.map((p) => (
                <div key={p.person.id} style={styles.participantItem}>
                  <div style={styles.participantAvatar}>
                    {p.person.first_name?.[0]}
                    {p.person.last_name?.[0]}
                  </div>
                  <div style={styles.participantInfo}>
                    <strong style={styles.participantName}>
                      {p.person.first_name} {p.person.last_name}
                    </strong>
                    <span style={styles.participantRole}>{p.role_in_project}</span>
                  </div>
                  {p.person.id === user?.id && <span style={styles.youBadge}>You</span>}
                </div>
              ))}
            </div>
          </section>

          {/* Project Metadata */}
          <section style={styles.sidebarSection}>
            <h3 style={styles.sidebarTitle}>Project Details</h3>
            <div style={styles.metadata}>
              <div style={styles.metadataItem}>
                <strong>Created:</strong>
                <span>
                  {project?.created_at ?
                    (() => {
                      try {
                        const date = new Date(project.created_at);
                        return isNaN(date.getTime()) ? 'Unknown' : date.toLocaleDateString();
                      } catch {
                        return 'Unknown';
                      }
                    })() : 'Unknown'
                  }
                </span>
              </div>
              <div style={styles.metadataItem}>
                <strong>Last Updated:</strong>
                <span>
                  {project?.updated_at || project?.last_updated ?
                    (() => {
                      try {
                        const date = new Date(project.updated_at || project.last_updated);
                        return isNaN(date.getTime()) ? 'Unknown' : date.toLocaleDateString();
                      } catch {
                        return 'Unknown';
                      }
                    })() : 'Unknown'
                  }
                </span>
              </div>
              <div style={styles.metadataItem}>
                <strong>Status:</strong>
                <span style={styles.statusText}>{project?.status}</span>
              </div>
            </div>
          </section>
        </aside>
      </div>

      {error && (
        <div style={styles.errorBanner}>
          <AlertCircle size={16} />
          <span>{error}</span>
          <button onClick={() => setError("")} style={styles.dismissError}>
            ×
          </button>
        </div>
      )}
    </div>
  );
};

const styles = {
  pageContainer: {
    minHeight: "100vh",
    background: "#f8fafc",
    display: "flex",
    flexDirection: "column",
  },
  header: {
    background: "white",
    borderBottom: "1px solid #e2e8f0",
    padding: "20px 40px",
  },
  headerContent: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
    maxWidth: "1400px",
    margin: "0 auto",
    width: "100%",
  },
  projectTitle: {
    margin: "0 0 8px 0",
    fontSize: "2rem",
    fontWeight: "700",
    color: "#1e293b",
  },
  headerMeta: {
    display: "flex",
    alignItems: "center",
    gap: "16px",
    flexWrap: "wrap",
  },
  statusBadge: {
    padding: "4px 12px",
    borderRadius: "16px",
    fontSize: "0.875rem",
    fontWeight: "500",
  },
  "status-draft": { background: "#f1f5f9", color: "#334155" },
  "status-in_progress": { background: "#dbeafe", color: "#1e40af" },
  "status-submitted": { background: "#fef3c7", color: "#92400e" },
  "status-published": { background: "#dcfce7", color: "#166534" },
  "status-completed": { background: "#dcfce7", color: "#166534" },
  "status-unknown": { background: "#f3f4f6", color: "#6b7280" },
  lastSaved: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    fontSize: "0.875rem",
    color: "#64748b",
  },
  savingIndicator: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    fontSize: "0.875rem",
    color: "#64748b",
  },
  headerActions: {
    display: "flex",
    gap: "8px",
  },
  refreshButton: {
    padding: "8px",
    border: "1px solid #e2e8f0",
    borderRadius: "6px",
    background: "white",
    color: "#64748b",
    cursor: "pointer",
    transition: "all 0.2s ease",
    ":hover": {
      borderColor: "#cbd5e1",
      color: "#374151",
    },
    ":disabled": {
      opacity: "0.6",
      cursor: "not-allowed",
    },
  },
  contentContainer: {
    flex: 1,
    display: "grid",
    gridTemplateColumns: "1fr 300px",
    gridTemplateRows: "auto 1fr",
    gap: "0",
    maxWidth: "1400px",
    margin: "0 auto",
    width: "100%",
    padding: "0 40px 40px",
  },
  tabContainer: {
    gridColumn: "1 / -1",
    display: "flex",
    borderBottom: "1px solid #e2e8f0",
    background: "white",
    marginBottom: "20px",
  },
  tab: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "12px 24px",
    border: "none",
    background: "none",
    color: "#64748b",
    cursor: "pointer",
    fontSize: "0.875rem",
    fontWeight: "500",
    transition: "all 0.2s ease",
    borderBottom: "2px solid transparent",
  },
  tabActive: {
    color: "#2563eb",
    borderBottomColor: "#2563eb",
  },
  mainContent: {
    background: "white",
    borderRadius: "8px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
    overflow: "hidden",
  },
  editorSection: {
    height: "100%",
  },
  chatSection: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
  },
  chatMessages: {
    flex: 1,
    padding: "20px",
    overflowY: "auto",
    maxHeight: "600px",
  },
  message: {
    marginBottom: "16px",
    padding: "12px",
    background: "#f8fafc",
    borderRadius: "8px",
    maxWidth: "80%",
  },
  messageOwn: {
    background: "#dbeafe",
    marginLeft: "auto",
    marginRight: "0",
  },
  messageHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "4px",
  },
  messageAuthor: {
    fontSize: "0.875rem",
    color: "#1e293b",
  },
  messageTime: {
    fontSize: "0.75rem",
    color: "#64748b",
  },
  messageContent: {
    margin: "0",
    fontSize: "0.875rem",
    lineHeight: "1.4",
  },
  sendingIndicator: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    fontSize: "0.75rem",
    color: "#64748b",
    marginTop: "4px",
  },
  emptyChat: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "300px",
    color: "#64748b",
    textAlign: "center",
  },
  emptyChatSubtitle: {
    fontSize: "0.875rem",
    margin: "4px 0 0 0",
  },
  chatForm: {
    display: "flex",
    gap: "8px",
    padding: "16px",
    borderTop: "1px solid #e2e8f0",
  },
  chatInput: {
    flex: 1,
    padding: "8px 12px",
    border: "1px solid #e2e8f0",
    borderRadius: "6px",
    fontSize: "0.875rem",
    ":focus": {
      outline: "none",
      borderColor: "#2563eb",
    },
    ":disabled": {
      background: "#f1f5f9",
      cursor: "not-allowed",
    },
  },
  chatButton: {
    padding: "8px 12px",
    border: "none",
    background: "#2563eb",
    color: "white",
    borderRadius: "6px",
    cursor: "pointer",
    ":disabled": {
      background: "#93c5fd",
      cursor: "not-allowed",
    },
  },
  chatDisabled: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "16px",
    borderTop: "1px solid #e2e8f0",
    color: "#64748b",
    fontSize: "0.875rem",
    background: "#f1f5f9",
  },
  sidebar: {
    paddingLeft: "20px",
  },
  sidebarSection: {
    background: "white",
    borderRadius: "8px",
    padding: "20px",
    marginBottom: "20px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
  },
  sidebarTitle: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    margin: "0 0 16px 0",
    fontSize: "1rem",
    fontWeight: "600",
    color: "#1e293b",
  },
  participantList: {
    display: "flex",
    flexDirection: "column",
    gap: "12px",
  },
  participantItem: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
    padding: "8px 0",
  },
  participantAvatar: {
    width: "32px",
    height: "32px",
    borderRadius: "50%",
    background: "#2563eb",
    color: "white",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontSize: "0.75rem",
    fontWeight: "600",
    flexShrink: 0,
  },
  participantInfo: {
    flex: 1,
    minWidth: 0,
  },
  participantName: {
    display: "block",
    fontSize: "0.875rem",
    fontWeight: "500",
    color: "#1e293b",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  participantRole: {
    fontSize: "0.75rem",
    color: "#64748b",
  },
  youBadge: {
    fontSize: "0.75rem",
    padding: "2px 6px",
    background: "#dbeafe",
    color: "#1e40af",
    borderRadius: "4px",
    fontWeight: "500",
  },
  metadata: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  metadataItem: {
    display: "flex",
    justifyContent: "space-between",
    fontSize: "0.875rem",
  },
  statusText: {
    fontWeight: "500",
    color: "#2563eb",
  },
  loadingContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100vh",
    color: "#64748b",
  },
  errorContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100vh",
    padding: "40px",
    textAlign: "center",
    color: "#dc2626",
  },
  errorBanner: {
    position: "fixed",
    bottom: "20px",
    left: "50%",
    transform: "translateX(-50%)",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "12px 16px",
    background: "#fef2f2",
    border: "1px solid #fecaca",
    borderRadius: "6px",
    color: "#dc2626",
    fontSize: "0.875rem",
    zIndex: 1000,
  },
  dismissError: {
    background: "none",
    border: "none",
    color: "#dc2626",
    cursor: "pointer",
    fontSize: "1.2rem",
    padding: "0",
    marginLeft: "8px",
  },
  retryButton: {
    padding: "8px 16px",
    border: "1px solid #dc2626",
    background: "white",
    color: "#dc2626",
    borderRadius: "6px",
    cursor: "pointer",
    marginTop: "16px",
    ":hover": {
      background: "#fef2f2",
    },
  },
  spinning: {
    animation: "spin 1s linear infinite",
  },
};

// // Add CSS animations
// const styleSheet = document.styleSheets[0];
// styleSheet.insertRule(
//   `
//   @keyframes spin {
//     from { transform: rotate(0deg); }
//     to { transform: rotate(360deg); }
//   }
// `,
//   styleSheet.cssRules.length
// );

export default ProjectDetailPage;
