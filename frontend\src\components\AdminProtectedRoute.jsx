import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import "./AdminProtectedRoute.css"; // We'll create a CSS file for styling

const AdminProtectedRoute = ({ children, requiredPermission = "can_supervise" }) => {
  const { user, loading, error } = useAuth();

  // Show loading state with a proper spinner
  if (loading) {
    return (
      <div className="auth-loading-container">
        <div className="auth-spinner"></div>
        <p>Verifying permissions...</p>
      </div>
    );
  }

  // Handle authentication errors
  if (error) {
    return (
      <div className="auth-error-container">
        <h2>Authentication Error</h2>
        <p>There was a problem verifying your credentials.</p>
        <button onClick={() => window.location.reload()} className="auth-retry-btn">
          Try Again
        </button>
      </div>
    );
  }

  // Check if user exists
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check if user has supervisor permissions
  // Updated to work with JWT token structure
  const hasPermission = () => {
    console.log("🔍 DEBUG AdminProtectedRoute: Full user object:", user);
    console.log("🔍 DEBUG AdminProtectedRoute: User type:", typeof user);
    console.log("🔍 DEBUG AdminProtectedRoute: User keys:", user ? Object.keys(user) : 'null');
    console.log("🔍 DEBUG AdminProtectedRoute: Required permission:", requiredPermission);

    // Check JWT token permissions directly
    if (requiredPermission === "can_supervise" && user.can_supervise) {
      console.log("🔍 DEBUG AdminProtectedRoute: User has can_supervise permission");
      return true;
    }

    if (requiredPermission === "can_invite" && user.can_invite) {
      console.log("🔍 DEBUG AdminProtectedRoute: User has can_invite permission");
      return true;
    }

    // Check role-based permissions
    const supervisorRoles = ["Professor", "Admin", "Administrator", "Supervisor", "Department Head"];
    const userRole = user.primary_role || user.primary_role_name;

    if (userRole && supervisorRoles.includes(userRole)) {
      console.log("🔍 DEBUG AdminProtectedRoute: User has supervisor role:", userRole);
      return true;
    }

    console.log("🔍 DEBUG AdminProtectedRoute: Permission denied");
    return false;
  };

  if (!hasPermission()) {
    return (
      <div className="permission-denied-container">
        <div className="permission-denied-content">
          <h2>Access Denied</h2>
          <p>You don't have permission to access this page.</p>
          <p>
            Required permission: <strong>{requiredPermission}</strong>
          </p>
          <div className="permission-denied-actions">
            <Navigate to="/dashboard" replace>
              <button className="permission-btn primary">Go to Dashboard</button>
            </Navigate>
            <button onClick={() => window.history.back()} className="permission-btn secondary">
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If all checks pass, render the requested admin page
  return children;
};

export default AdminProtectedRoute;



