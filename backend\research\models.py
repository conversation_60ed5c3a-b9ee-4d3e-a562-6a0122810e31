# AcademiaSpace/backend/research/models.py

import os
import uuid
from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from django.conf import settings
from django.core.exceptions import ValidationError
from django.contrib.postgres.search import SearchVectorField
from django.contrib.postgres.indexes import GinIndex

# Import the definitive models from the accounts app
from accounts.models import ResearchGroup, CustomUser

# ==============================================================================
# SECTION 1: PERSONAL RESEARCH TOOLS
# ==============================================================================

class Paper(models.Model):
    """A single academic paper fetched from an external source for a specific user."""
    class AccessType(models.TextChoices):
        OPEN_ACCESS = 'OPEN_ACCESS', 'Open Access'
        PAYWALLED = 'PAYWALLED', 'Paywalled'
        UNKNOWN = 'UNKNOWN', 'Unknown'

    class ConfidenceLevel(models.TextChoices):
        HIGH = 'HIGH', 'High'
        MEDIUM = 'MEDIUM', 'Medium'
        LOW = 'LOW', 'Low'

    class PaperStatus(models.TextChoices):
        NEW = 'NEW', 'New'
        REVIEWED = 'REVIEWED', 'Reviewed'
        RELEVANT = 'RELEVANT', 'Relevant'
        IRRELEVANT = 'IRRELEVANT', 'Irrelevant'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    owner = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="papers")
    research_group = models.ForeignKey(ResearchGroup, on_delete=models.CASCADE, related_name="papers")
    title = models.CharField(max_length=500, blank=True, null=True)
    authors = models.JSONField(default=list, blank=True)
    abstract = models.TextField(blank=True, null=True)
    source_journal = models.CharField(max_length=300, blank=True, null=True)
    publication_date = models.DateField(blank=True, null=True)
    doi = models.CharField(max_length=200, blank=True, null=True)
    url = models.URLField(blank=True, null=True)
    access_type = models.CharField(max_length=20, choices=AccessType.choices, default=AccessType.UNKNOWN)
    status = models.CharField(max_length=15, choices=PaperStatus.choices, default=PaperStatus.NEW)
    is_priority = models.BooleanField(default=False)
    retrieved_date = models.DateField(auto_now_add=True)
    tags = models.JSONField(default=list, blank=True)
    summary = models.TextField(blank=True, null=True)
    confidence = models.CharField(max_length=10, choices=ConfidenceLevel.choices, default=ConfidenceLevel.LOW)
    confidence_reason = models.CharField(max_length=255, default="Initial assessment")
    is_favorite = models.BooleanField(default=False)
    citation_count = models.IntegerField(default=0)
    search_vector = SearchVectorField(null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['owner', 'doi'], name='unique_paper_per_user', condition=models.Q(doi__isnull=False))
        ]
        ordering = ['-retrieved_date']
        indexes = [GinIndex(fields=['search_vector'])]

    def clean(self):
        if self.owner and self.research_group:
            if not self.owner.group_memberships.filter(research_group=self.research_group, status='ACTIVE').exists():
                raise ValidationError("The paper owner must be an active member of the selected research group.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"'{self.title[:50] if self.title else 'No Title'}' for {self.owner.username}"

class Note(models.Model):
    """A user's private notes for a specific research paper."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    paper = models.ForeignKey(Paper, on_delete=models.CASCADE, related_name="notes")
    owner = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="notes")
    content = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('paper', 'owner')
        ordering = ['-last_updated']

# ==============================================================================
# SECTION 2: COLLABORATIVE RESEARCH PROJECTS (The "Work")
# ==============================================================================

class Project(models.Model):
    """Represents a single, distinct research project (e.g., a thesis, grant, or paper)."""
    class Status(models.TextChoices):
        DRAFT = 'DRAFT', 'Draft'
        IN_PROGRESS = 'IN_PROGRESS', 'In Progress'
        SUBMITTED = 'SUBMITTED', 'Submitted'
        PUBLISHED = 'PUBLISHED', 'Published'
        COMPLETED = 'COMPLETED', 'Completed'

    class ProjectType(models.TextChoices):
        THESIS = 'THESIS', 'Thesis/Dissertation'
        GRANT = 'GRANT', 'Grant Proposal'
        PAPER = 'PAPER', 'Research Paper'
        OTHER = 'OTHER', 'Other'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=500)
    description = models.TextField(blank=True)
    project_type = models.CharField(max_length=20, choices=ProjectType.choices, default=ProjectType.PAPER)
    research_group = models.ForeignKey(ResearchGroup, on_delete=models.CASCADE, related_name='projects')
    content = models.JSONField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.DRAFT)
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    class Meta:
        ordering = ['-last_updated']

class ProjectParticipation(models.Model):
    """Junction table linking a User to a Project with a specific role for that project."""
    class ProjectRole(models.TextChoices):
        LEAD_RESEARCHER = 'LEAD_RESEARCHER', 'Lead Researcher'
        PRIMARY_SUPERVISOR = 'PRIMARY_SUPERVISOR', 'Primary Supervisor'
        CO_SUPERVISOR = 'CO_SUPERVISOR', 'Co-Supervisor'
        COLLABORATOR = 'COLLABORATOR', 'Collaborator'
        STUDENT = 'STUDENT', 'Student'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='participations')
    person = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='project_participations')
    role_in_project = models.CharField(max_length=30, choices=ProjectRole.choices)
    date_added = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    def clean(self):
        if not self.person.group_memberships.filter(research_group=self.project.research_group, status='ACTIVE').exists():
            raise ValidationError("Project participants must be active members of the project's research group.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.person.username} as {self.get_role_in_project_display()} on '{self.project.title}'"
    class Meta:
        unique_together = ('project', 'person')

class ProjectMessage(models.Model):
    """A message or file shared within a Project's chat space."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='messages')
    author = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='project_messages')
    content = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message by {self.author.username} in project '{self.project.title[:20]}...'"



