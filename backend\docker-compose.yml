# /backend/docker-compose.yml
services:
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file: [".env"]
  redis:
    image: redis:7-alpine
    ports: ["6379:6379"]
  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
    ports: ["8000:8000"]
    env_file: [".env"]
    depends_on: [db, redis]
  worker:
    build: .
    command: celery -A academiaspace worker -l info
    volumes:
      - .:/app
    env_file: [".env"]
    depends_on: [web, redis]
  beat:
    build: .
    command: celery -A academiaspace beat -l info
    volumes:
      - .:/app
    env_file: [".env"]
    depends_on: [web, redis]
volumes:
  postgres_data:



  