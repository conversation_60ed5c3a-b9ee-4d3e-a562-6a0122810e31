/* Loading Styles */
.auth-loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 50vh;
    text-align: center;
}

.auth-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary, #3498db);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Styles */
.auth-error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 50vh;
    text-align: center;
    padding: 2rem;
}

.auth-error-container h2 {
    color: #e74c3c;
    margin-bottom: 1rem;
}

.auth-retry-btn {
    background-color: var(--primary, #3498db);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.2s;
}

.auth-retry-btn:hover {
    background-color: var(--primary-dark, #2980b9);
}

/* Permission Denied Styles */
.permission-denied-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
    padding: 2rem;
}

.permission-denied-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
    text-align: center;
    max-width: 500px;
    width: 100%;
}

.permission-denied-content h2 {
    color: #e74c3c;
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.permission-denied-content p {
    margin-bottom: 1rem;
    color: #555;
    line-height: 1.5;
}

.permission-denied-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.permission-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 140px;
}

.permission-btn.primary {
    background-color: var(--primary, #3498db);
    color: white;
    border: none;
}

.permission-btn.primary:hover {
    background-color: var(--primary-dark, #2980b9);
}

.permission-btn.secondary {
    background-color: transparent;
    color: #555;
    border: 1px solid #ddd;
}

.permission-btn.secondary:hover {
    background-color: #f8f9fa;
    border-color: #ccc;
}

/* Responsive Design */
@media (max-width: 576px) {
    .permission-denied-content {
        padding: 1.5rem;
    }
    
    .permission-denied-actions {
        flex-direction: column;
    }
    
    .permission-btn {
        width: 100%;
    }
}


