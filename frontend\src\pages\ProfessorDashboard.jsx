import React, { useState, useEffect } from 'react';
import { 
  Users, 
  UserCheck, 
  UserPlus, 
  GraduationCap, 
  RefreshCw, 
  Briefcase,
  BarChart3,
  BookOpen,
  Play,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import WorkspaceComponent from '../components/WorkspaceComponent';
import { useAuth } from '../context/AuthContext';
import { 
  getMyStudents, 
  startSupervision, 
  getSupervisionRequests,
  approveSupervisionRequest,
  rejectSupervisionRequest 
} from '../services/api';

const ProfessorDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("workspace");
  const [students, setStudents] = useState([]);
  const [supervisionRequests, setSupervisionRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (activeTab === "students") {
      fetchStudents();
      fetchSupervisionRequests();
    }
  }, [activeTab]);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      const response = await getMyStudents();
      setStudents(response.data || []);
    } catch (err) {
      setError("Failed to load students");
    } finally {
      setLoading(false);
    }
  };

  const fetchSupervisionRequests = async () => {
    try {
      const response = await getSupervisionRequests();
      setSupervisionRequests(response.data || []);
    } catch (err) {
      console.error("Failed to load supervision requests:", err);
    }
  };

  const handleStartSupervision = async (studentId) => {
    try {
      await startSupervision(studentId);
      fetchStudents(); // Refresh the list
    } catch (err) {
      setError("Failed to start supervision");
    }
  };

  const handleApproveRequest = async (requestId) => {
    try {
      await approveSupervisionRequest(requestId);
      fetchSupervisionRequests();
      fetchStudents();
    } catch (err) {
      setError("Failed to approve request");
    }
  };

  const handleRejectRequest = async (requestId) => {
    try {
      await rejectSupervisionRequest(requestId);
      fetchSupervisionRequests();
    } catch (err) {
      setError("Failed to reject request");
    }
  };

  const renderStudentsTab = () => (
    <div style={styles.studentsContent}>
      {/* Supervision Requests */}
      {supervisionRequests.length > 0 && (
        <div style={styles.section}>
          <h3 style={styles.sectionTitle}>Pending Supervision Requests</h3>
          <div style={styles.requestsGrid}>
            {supervisionRequests.map((request) => (
              <div key={request.id} style={styles.requestCard}>
                <div style={styles.requestHeader}>
                  <div>
                    <h4>{request.student.first_name} {request.student.last_name}</h4>
                    <p style={styles.requestEmail}>{request.student.email}</p>
                  </div>
                  <span style={styles.requestDate}>
                    <Clock size={14} />
                    {new Date(request.created_at).toLocaleDateString()}
                  </span>
                </div>
                <p style={styles.requestMessage}>{request.message || "No message provided"}</p>
                <div style={styles.requestActions}>
                  <button 
                    onClick={() => handleApproveRequest(request.id)}
                    style={styles.approveButton}
                  >
                    <CheckCircle size={16} />
                    Approve
                  </button>
                  <button 
                    onClick={() => handleRejectRequest(request.id)}
                    style={styles.rejectButton}
                  >
                    <XCircle size={16} />
                    Reject
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Current Students */}
      <div style={styles.section}>
        <h3 style={styles.sectionTitle}>My Students</h3>
        {loading ? (
          <div style={styles.loadingContainer}>
            <RefreshCw size={24} className="spinning" />
            <p>Loading students...</p>
          </div>
        ) : students.length === 0 ? (
          <div style={styles.emptyState}>
            <GraduationCap size={48} />
            <p>No students under supervision yet</p>
            <p style={styles.emptySubtext}>Students will appear here once you start supervising them</p>
          </div>
        ) : (
          <div style={styles.studentsGrid}>
            {students.map((student) => (
              <div key={student.id} style={styles.studentCard}>
                <div style={styles.studentHeader}>
                  <div style={styles.studentAvatar}>
                    <Users size={24} />
                  </div>
                  <div style={styles.studentInfo}>
                    <h4>{student.first_name} {student.last_name}</h4>
                    <p style={styles.studentEmail}>{student.email}</p>
                    <p style={styles.studentRole}>{student.primary_role || 'Student'}</p>
                  </div>
                </div>
                
                <div style={styles.studentStats}>
                  <div style={styles.stat}>
                    <span style={styles.statLabel}>Projects</span>
                    <span style={styles.statValue}>{student.project_count || 0}</span>
                  </div>
                  <div style={styles.stat}>
                    <span style={styles.statLabel}>Papers</span>
                    <span style={styles.statValue}>{student.paper_count || 0}</span>
                  </div>
                </div>

                <div style={styles.studentActions}>
                  {student.supervision_status === 'accepted' ? (
                    <div style={styles.supervisionActive}>
                      <UserCheck size={16} />
                      <span>Under Supervision</span>
                    </div>
                  ) : (
                    <button 
                      onClick={() => handleStartSupervision(student.id)}
                      style={styles.startSupervisionButton}
                    >
                      <Play size={16} />
                      Start Supervision
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <div style={styles.headerContent}>
          <h1 style={styles.title}>Professor Dashboard</h1>
          <p style={styles.subtitle}>Welcome back, Prof. {user?.first_name || user?.username}!</p>
        </div>
        <div style={styles.headerActions}>
          <button onClick={fetchStudents} style={styles.refreshButton} title="Refresh Data">
            <RefreshCw size={16} />
          </button>
        </div>
      </div>

      <div style={styles.tabs}>
        <button 
          onClick={() => setActiveTab("workspace")} 
          style={activeTab === "workspace" ? styles.tabActive : styles.tab}
        >
          <Briefcase size={16} /> Workspace
        </button>
        <button 
          onClick={() => setActiveTab("students")} 
          style={activeTab === "students" ? styles.tabActive : styles.tab}
        >
          <GraduationCap size={16} /> Students
        </button>
        <button 
          onClick={() => setActiveTab("overview")} 
          style={activeTab === "overview" ? styles.tabActive : styles.tab}
        >
          <BarChart3 size={16} /> Overview
        </button>
        <button 
          onClick={() => setActiveTab("research")} 
          style={activeTab === "research" ? styles.tabActive : styles.tab}
        >
          <BookOpen size={16} /> Research
        </button>
      </div>

      {error && (
        <div style={styles.errorMessage}>
          {error}
        </div>
      )}

      {activeTab === "workspace" && <WorkspaceComponent />}
      {activeTab === "students" && renderStudentsTab()}
      {activeTab === "overview" && (
        <div style={styles.card}>
          <h2>Research Overview</h2>
          <p>Your research metrics and analytics will appear here.</p>
        </div>
      )}
      {activeTab === "research" && (
        <div style={styles.card}>
          <h2>Research Management</h2>
          <p>Manage your research projects, publications, and collaborations.</p>
        </div>
      )}
    </div>
  );
};

const styles = {
  container: { 
    maxWidth: "1200px", 
    margin: "20px auto", 
    fontFamily: "system-ui, sans-serif",
    padding: "0 20px"
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "30px",
    padding: "20px 0",
    borderBottom: "1px solid #e2e8f0"
  },
  headerContent: {
    flex: 1
  },
  title: {
    margin: "0 0 8px 0",
    fontSize: "2rem",
    fontWeight: "600",
    color: "#1e293b"
  },
  subtitle: {
    margin: 0,
    color: "#64748b",
    fontSize: "1.1rem"
  },
  headerActions: {
    display: "flex",
    gap: "12px"
  },
  refreshButton: {
    background: "#14b8a6",
    color: "white",
    border: "none",
    padding: "8px",
    borderRadius: "6px",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    transition: "background-color 0.2s ease"
  },
  tabs: {
    display: "flex",
    gap: "8px",
    marginBottom: "20px",
    borderBottom: "1px solid #e2e8f0",
    paddingBottom: "0"
  },
  tab: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "12px 24px",
    border: "none",
    background: "none",
    color: "#64748b",
    cursor: "pointer",
    fontSize: "0.875rem",
    fontWeight: "500",
    transition: "all 0.2s ease",
    borderBottom: "2px solid transparent"
  },
  tabActive: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "12px 24px",
    border: "none",
    background: "none",
    color: "#14b8a6",
    cursor: "pointer",
    fontSize: "0.875rem",
    fontWeight: "500",
    transition: "all 0.2s ease",
    borderBottom: "2px solid #14b8a6"
  },
  errorMessage: {
    background: "#fee2e2",
    color: "#dc2626",
    padding: "12px 20px",
    borderRadius: "6px",
    marginBottom: "20px"
  },
  card: {
    background: 'white',
    padding: '25px',
    borderRadius: '8px',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e2e8f0'
  },
  // Students tab styles
  studentsContent: {
    marginTop: "20px"
  },
  section: {
    marginBottom: "40px"
  },
  sectionTitle: {
    fontSize: "1.25rem",
    fontWeight: "600",
    marginBottom: "20px",
    color: "#1e293b"
  },
  requestsGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fill, minmax(350px, 1fr))",
    gap: "20px",
    marginBottom: "30px"
  },
  requestCard: {
    background: "white",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    padding: "20px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
  },
  requestHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: "12px"
  },
  requestEmail: {
    color: "#64748b",
    fontSize: "0.875rem",
    margin: "4px 0 0 0"
  },
  requestDate: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    color: "#64748b",
    fontSize: "0.75rem"
  },
  requestMessage: {
    color: "#374151",
    fontSize: "0.875rem",
    marginBottom: "16px",
    fontStyle: "italic"
  },
  requestActions: {
    display: "flex",
    gap: "8px"
  },
  approveButton: {
    display: "flex",
    alignItems: "center",
    gap: "6px",
    background: "#10b981",
    color: "white",
    border: "none",
    padding: "8px 12px",
    borderRadius: "6px",
    cursor: "pointer",
    fontSize: "0.875rem"
  },
  rejectButton: {
    display: "flex",
    alignItems: "center",
    gap: "6px",
    background: "#ef4444",
    color: "white",
    border: "none",
    padding: "8px 12px",
    borderRadius: "6px",
    cursor: "pointer",
    fontSize: "0.875rem"
  },
  loadingContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "200px",
    color: "#64748b"
  },
  emptyState: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "200px",
    color: "#64748b"
  },
  emptySubtext: {
    fontSize: "0.875rem",
    marginTop: "8px"
  },
  studentsGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fill, minmax(320px, 1fr))",
    gap: "20px"
  },
  studentCard: {
    background: "white",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    padding: "20px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
    transition: "box-shadow 0.2s ease"
  },
  studentHeader: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
    marginBottom: "16px"
  },
  studentAvatar: {
    width: "48px",
    height: "48px",
    background: "#f1f5f9",
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: "#64748b"
  },
  studentInfo: {
    flex: 1
  },
  studentEmail: {
    color: "#64748b",
    fontSize: "0.875rem",
    margin: "2px 0"
  },
  studentRole: {
    color: "#14b8a6",
    fontSize: "0.75rem",
    fontWeight: "500",
    textTransform: "uppercase"
  },
  studentStats: {
    display: "flex",
    gap: "20px",
    marginBottom: "16px",
    padding: "12px 0",
    borderTop: "1px solid #f1f5f9",
    borderBottom: "1px solid #f1f5f9"
  },
  stat: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center"
  },
  statLabel: {
    fontSize: "0.75rem",
    color: "#64748b",
    marginBottom: "4px"
  },
  statValue: {
    fontSize: "1.25rem",
    fontWeight: "600",
    color: "#1e293b"
  },
  studentActions: {
    display: "flex",
    justifyContent: "center"
  },
  startSupervisionButton: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    background: "#14b8a6",
    color: "white",
    border: "none",
    padding: "10px 16px",
    borderRadius: "6px",
    cursor: "pointer",
    fontSize: "0.875rem",
    fontWeight: "500",
    transition: "background-color 0.2s ease"
  },
  supervisionActive: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    color: "#10b981",
    fontSize: "0.875rem",
    fontWeight: "500"
  }
};

export default ProfessorDashboard;
