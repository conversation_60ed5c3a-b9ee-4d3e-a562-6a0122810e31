// frontend/src/pages/ProjectsListPage.jsx

import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
// --- CONSOLIDATED IMPORTS ---
import { getMyProjects, createNewProject } from "../services/api";
import ProjectCard from "../components/ProjectCard";

const ProjectsListPage = () => {
  // --- STATE MANAGEMENT ---
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const navigate = useNavigate();

  // --- DATA FETCHING ---
  const fetchProjects = useCallback(async () => {
    setLoading(true);
    setError(""); // Reset error on re-fetch
    try {
      const response = await getMyProjects();
      // Robustly handle both paginated and non-paginated API responses
      const projectsData = Array.isArray(response.data) ? response.data : response.data?.results || [];
      setProjects(projectsData);
    } catch (err) {
      console.error("Failed to fetch projects:", err);
      setError(err.response?.data?.detail || "Could not load your projects.");
    } finally {
      setLoading(false);
    }
  }, []);

  // Effect to fetch projects on initial component mount
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]); // useCallback ensures fetchProjects is stable

  // --- EVENT HANDLERS ---
  const handleCreateNewProject = async () => {
    const title = prompt("Please enter a title for your new research project:");

    if (title && title.trim() !== "") {
      setIsCreating(true);
      try {
        const response = await createNewProject({
          title: title,
          project_type: "RESEARCH", // Default to a standard research project
          status: "DRAFT",
        });
        const newProject = response.data;
        // Navigate to the new project's detail page immediately
        navigate(`/projects/${newProject.id}`);
      } catch (err) {
        console.error("Failed to create project:", err);
        alert(`Error: ${err.response?.data?.detail || "Could not create the project."}`);
      } finally {
        setIsCreating(false);
      }
    }
  };

  // --- RENDER LOGIC ---
  return (
    <div className="container" style={{ maxWidth: "1200px", margin: "20px auto", padding: "0 20px" }}>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "30px" }}>
        <div>
          <h1>My Projects</h1>
          <p style={{ marginTop: "-10px", color: "#64748b" }}>All projects where you are an active participant.</p>
        </div>

        {/* Only show the "+ New" button if there are already projects */}
        {projects.length > 0 && (
          <button onClick={handleCreateNewProject} disabled={isCreating} style={styles.button}>
            {isCreating ? "Creating..." : "+ Start New Project"}
          </button>
        )}
      </div>

      {loading && <div>Loading your projects...</div>}

      {error && (
        <div style={{ color: "red", textAlign: "center", padding: "20px" }}>
          <span>{error}</span>
          <button onClick={fetchProjects} style={{ marginLeft: "10px" }}>
            Try Again
          </button>
        </div>
      )}

      {!loading && !error && (
        <>
          {projects.length > 0 ? (
            <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: "20px" }}>
              {projects.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          ) : (
            <div style={{ textAlign: "center", padding: "50px", background: "#f8fafc", borderRadius: "8px" }}>
              <h2>You have no projects yet</h2>
              <p style={{ color: "#64748b", marginBottom: "20px" }}>
                Get started by creating your first collaborative research project.
              </p>
              <button onClick={handleCreateNewProject} disabled={isCreating} style={styles.button}>
                {isCreating ? "Creating..." : "Create Your First Project"}
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// Simple inline styles for demonstration
const styles = {
  button: {
    background: "#3b82f6",
    color: "white",
    border: "none",
    padding: "10px 15px",
    borderRadius: "6px",
    cursor: "pointer",
    fontWeight: "bold",
    fontSize: "0.9rem",
    display: "flex",
    alignItems: "center",
    gap: "5px",
  },
};

export default ProjectsListPage;



